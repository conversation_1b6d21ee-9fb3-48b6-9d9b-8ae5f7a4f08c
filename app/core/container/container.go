// Package container 容器
package container

import (
	"log"
	"strings"
	"sync"

	"lsservice/app/global/my_errors"
	"lsservice/app/global/variable"
)

// 定义一个全局键值对存储容器
var sMap sync.Map

// CreateContainersFactory 创建一个容器工厂
func CreateContainersFactory() *Containers {
	return &Containers{}
}

// 定义一个容器结构体
type Containers struct{}

// Set  1.以键值对的形式将代码注册到容器
func (c *Containers) Set(key string, value any) (res bool) {
	if _, exists := c.KeyIsExists(key); !exists {
		sMap.Store(key, value)
		res = true
	} else {
		// 程序启动阶段，zaplog 未初始化，使用系统log打印启动时候发生的异常日志
		if variable.ZapLog == nil {
			log.Fatal(my_errors.ErrorsContainerKeyAlreadyExists + ",请解决键名重复问题,相关键：" + key)
		} else {
			// 程序启动初始化完成
			variable.ZapLog.Warn(my_errors.ErrorsContainerKeyAlreadyExists + ", 相关键：" + key)
		}
	}
	return
}

// Delete  2.删除
func (c *Containers) Delete(key string) {
	sMap.Delete(key)
}

// Get 3.传递键，从容器获取值
func (c *Containers) Get(key string) any {
	if value, exists := c.KeyIsExists(key); exists {
		return value
	}
	return nil
}

// KeyIsExists 4. 判断键是否被注册
func (c *Containers) KeyIsExists(key string) (any, bool) {
	return sMap.Load(key)
}

// FuzzyDelete 按照键的前缀模糊删除容器中注册的内容
func (c *Containers) FuzzyDelete(keyPre string) {
	sMap.Range(func(key, value any) bool {
		if keyname, ok := key.(string); ok {
			if strings.HasPrefix(keyname, keyPre) {
				sMap.Delete(keyname)
			}
		}
		return true
	})
}
