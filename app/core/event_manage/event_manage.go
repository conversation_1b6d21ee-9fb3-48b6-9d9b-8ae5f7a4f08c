// Package event_manage 事件管理
package event_manage

import (
	"strings"
	"sync"

	"lsservice/app/global/my_errors"
	"lsservice/app/global/variable"
)

// 定义一个全局事件存储变量，本模块只负责存储 键 => 函数 ， 相对容器来说功能稍弱，但是调用更加简单、方便、快捷
var sMap sync.Map

// CreateEventManageFactory 创建一个事件管理工厂
func CreateEventManageFactory() *EventManage {
	return &EventManage{}
}

// EventManage 定义一个事件管理结构体
type EventManage struct{}

// 1.注册事件
func (e *EventManage) Set(key string, keyFunc func(args ...any)) bool {
	// 判断key下是否已有事件
	if _, exists := e.Get(key); !exists {
		sMap.Store(key, keyFunc)
		return true
	} else {
		variable.ZapLog.Info(my_errors.ErrorsFuncEventAlreadyExists + " , 相关键名：" + key)
	}
	return false
}

// 2.获取事件
func (e *EventManage) Get(key string) (any, bool) {
	if value, exists := sMap.Load(key); exists {
		return value, exists
	}
	return nil, false
}

// 3.执行事件
func (e *EventManage) Call(key string, args ...any) {
	if valueInterface, exists := e.Get(key); exists {
		if fn, ok := valueInterface.(func(args ...any)); ok {
			fn(args...)
		} else {
			variable.ZapLog.Error(my_errors.ErrorsFuncEventNotCall + ", 键名：" + key + ", 相关函数无法调用")
		}
	} else {
		variable.ZapLog.Error(my_errors.ErrorsFuncEventNotRegister + ", 键名：" + key)
	}
}

// 4.删除事件
func (e *EventManage) Delete(key string) {
	sMap.Delete(key)
}

// 5.根据键的前缀，模糊调用. 使用请谨慎.
func (e *EventManage) FuzzyCall(keyPre string) {
	sMap.Range(func(key, value any) bool {
		if keyName, ok := key.(string); ok {
			if strings.HasPrefix(keyName, keyPre) {
				e.Call(keyName)
			}
		}
		return true
	})
}
