package authorization

import (
	"lsservice/app/global/consts"
	"lsservice/app/service"
	"lsservice/utils/response"

	"github.com/gin-gonic/gin"
)

// type HeaderParams struct {
// 	Authorization string `header:"Authorization" binding:"required,min=20"`
// }

// 获取 Cookie, 检查 Session 有效性
func CheckSessionAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Session 是否存在
		session := service.Session.Get(ctx, consts.CookieServiceName)
		if session == "" {
			response.SessionErrorParam(ctx, consts.SessionNotFound)
			return
		}

		// 校验 Session
		if isOnline := service.LogOnline.Verify(ctx, session); !isOnline {
			response.SessionErrorParam(ctx, consts.SessionCheckError)
			return
		}

		// 更新 Session 时间
		if err := service.LogOnline.UpdateDate(ctx, session); err != nil {
			response.SessionErrorParam(ctx, consts.SessionUpdateDateError)
			return
		}
	}
}

// CheckTokenAuth 检查token完整性、有效性中间件
// func CheckTokenAuth() gin.HandlerFunc {
// 	return func(ctx *gin.Context) {
// 		headerParams := HeaderParams{}

// 		//  推荐使用 ShouldBindHeader 方式获取头参数
// 		if err := ctx.ShouldBindHeader(&headerParams); err != nil {
// 			response.TokenErrorParam(ctx, consts.JwtTokenMustValid+err.Error())
// 			return
// 		}

// 		// token := strings.Split(headerParams.Authorization, " ")

// 		fmt.Print(headerParams.Authorization)
// 	}
// }

// RefreshTokenConditionCheck 刷新token条件检查中间件，针对已经过期的token，要求是token格式以及携带的信息满足配置参数即可
func RefreshTokenConditionCheck() gin.HandlerFunc {
	return func(ctx *gin.Context) {
	}
}

// casbin检查用户对应的角色权限是否允许访问接口
func CheckCasbinAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
	}
}

// CheckCaptchaAuth 验证码中间件
func CheckCaptchaAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
	}
}
