package cors

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// 处理跨域请求,支持 options 访问
func Next() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		origin := ctx.GetHeader("Origin")
		if len(origin) == 0 {
			ctx.Next()
			return
		}

		// 同源直接过
		host := ctx.GetHeader("Host")
		if origin == "http://"+host || origin == "https://"+host {
			ctx.Next()
			return
		}

		ctx.Header("Access-Control-Allow-Origin", "*")
		ctx.Header("Access-Control-Allow-Headers", "Access-Control-Allow-Headers,Authorization,User-Agent, Keep-Alive, Content-Type, X-Requested-With,X-CSRF-Token,AccessToken,Token")
		ctx.Header("Access-Control-Allow-Methods", "GET, POST, DELETE, PUT, PATCH, OPTIONS")
		ctx.Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")
		ctx.Header("Access-Control-Allow-Credentials", "true")

		// 放行所有 OPTIONS 方法
		method := ctx.Request.Method
		if method == "OPTIONS" {
			ctx.AbortWithStatus(http.StatusNoContent)
			ctx.Abort()
		}
		ctx.Next()
	}
}
