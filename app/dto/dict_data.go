package dto

// 字典数据分页查询条件
type DictDataPageReq struct {
	DictType  string `form:"type"`   // 字典类型
	DictLabel string `form:"label"`  // 字典标签
	State     string `form:"state"`  // 字典状态
	Remark    string `form:"remark"` // 备注
	PageReq
}

// 新增字典数据
type DictDataInsertReq struct {
	DictLabel string  `json:"label"`
	DictValue string  `json:"value" binding:"required"`
	DictType  string  `json:"type"`
	Sort      uint    `json:"sort"`
	CssClass  *string `json:"cssClass"`
	ListClass *string `json:"listClass"`
	IsDefault string  `json:"isDefault"`
	State     string  `json:"state"`
	Remark    string  `json:"remark"`
}

// 更新字典数据
type DictDataUpdateReq struct {
	DictDataInsertReq
}
