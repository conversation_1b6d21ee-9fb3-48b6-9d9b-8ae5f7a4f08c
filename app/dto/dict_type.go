package dto

// 字典类型分页查询条件
type DictTypePageReq struct {
	DictName  string `form:"name"`      // 字典名称
	DictType  string `form:"type"`      // 字典类型
	State     string `form:"state"`     // 字典状态
	CreatedAt string `form:"createdAt"` // 创建时间
	PageReq
}

// 新增字典类型
type DictTypeInsertReq struct {
	DictName string `json:"name"`
	DictType string `json:"type" binding:"required"`
	State    string `json:"state"`
	Remark   string `json:"remark"`
}

// 更新字典类型
type DictTypeUpdateReq struct {
	DictTypeInsertReq
}
