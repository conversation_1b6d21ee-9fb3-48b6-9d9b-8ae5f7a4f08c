package dto

// 角色分页查询条件
type RolePageReq struct {
	RoleCode  string `form:"code"`      // 权限编码
	RoleName  string `form:"name"`      // 角色名称
	State     string `form:"state"`     // 角色状态
	CreatedAt string `form:"createdAt"` // 创建时间
	PageReq
}

// 新增角色
type RoleInsertReq struct {
	RoleCode string  `json:"code" binding:"required"`
	RoleName string  `json:"name"`
	Sort     uint    `json:"sort"`
	State    string  `json:"state"`
	Remark   string  `json:"remark"`
	MenuIDs  *string `json:"menuIds"`
}

// 更新角色
type RoleUpdateReq struct {
	// ID int `form:"id" binding:"required"`
	RoleInsertReq
}

// 设置数据范围
type RoleDateScopeReq struct {
	RoleCode  string  `json:"code" binding:"required"`
	RoleName  string  `json:"name"`
	DataScope string  `json:"dataScope"`
	DeptIDs   *string `json:"deptIds"`
}
