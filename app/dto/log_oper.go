package dto

// 登录日志分页查询条件
type LogOperPageReq struct {
	Module       string `form:"module"`   // 系统模块
	BusinessType string `form:"type"`     // 业务类型
	OperName     string `form:"operName"` // 操作人员
	OperAt       string `form:"operAt"`   // 操作时间
	PageReq
}

type LogOperInsertReq struct {
	Module        string `json:"module"`
	BusinessType  string `json:"type"`
	Method        string `json:"method"`
	RequestMethod string `json:"requestMethod"`
	OperatorType  string `json:"operatorType"`
	OperName      string `json:"operName"`
	Url           string `json:"url"`
	IpAddr        string `json:"ipAddr"`
	Location      string `json:"location"`
	Param         string `json:"param"`
	StatusCode    int    `json:"status"`
	JsonResult    string `json:"result"`
	ErrorMsg      string `json:"errorMsg"`
	CostTime      uint   `json:"costTime"`
}
