package dto

// 分页条件
type PageReq struct {
	Page   *int   `form:"page" binding:"required"`  // 页码
	Limit  int    `form:"limit" binding:"required"` // 每页条数
	SortBy string `form:"sortby"`
	Order  string `form:"order"`
}

// 设置状态
type StateReq struct {
	// ID int `form:"id" binding:"required"`
	State string `json:"state" binding:"required"`
}

// 查询ID
type UriID struct {
	ID string `uri:"id" binding:"required"`
}

// 查询IDs
type UriIDs struct {
	IDs string `uri:"ids" binding:"required"`
}

// 导入
type ImportReq struct {
	File string `json:"file" binding:"required"`
}

// 导出查询
type ExportReq struct {
	CreatedAt string `form:"createdAt"` // 创建时间
}
