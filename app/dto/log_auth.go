package dto

import (
	"lsservice/app/model"
)

// 登录日志分页查询条件
type LogAuthPageReq struct {
	IpAddr   string `form:"ipAddr"`   // 登录地址
	UserName string `form:"userName"` // 登录名称
	Status   string `form:"status"`   // 登录状态
	AuthAt   string `form:"authAt"`   // 登录时间
	PageReq
}

type LogAuthInsertReq struct {
	UserName string `json:"userName"`
	model.AuthInfoModel
	Status int    `json:"status"`
	Type   string `json:"type"`
	Msg    string `json:"msg"`
}
