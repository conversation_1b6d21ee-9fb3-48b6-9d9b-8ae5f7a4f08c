package dto

// 菜单查询条件
type MenuReq struct {
	MenuName string `form:"name"`    // 菜单名称
	Visible  string `form:"visible"` // 菜单状态
}

// 新增菜单
type MenuInsertReq struct {
	MenuPid   uint    `json:"menuPid"`
	MenuType  string  `json:"type"`
	MenuCode  string  `json:"code" binding:"required"`
	MenuName  string  `json:"name"`
	Sort      uint    `json:"sort"`
	Path      string  `json:"path"`
	Target    string  `json:"target"`
	Visible   string  `json:"visible"`
	IsRefresh string  `json:"isRefresh"`
	Perms     *string `json:"perms"`
	Icon      string  `json:"icon"`
	Remark    string  `json:"remark"`
}

// 更新菜单
type MenuUpdateReq struct {
	MenuInsertReq
}

// 设置状态
type MenuVisibleReq struct {
	Visible string `json:"visible" binding:"required"`
}
