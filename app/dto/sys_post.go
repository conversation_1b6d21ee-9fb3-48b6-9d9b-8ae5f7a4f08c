package dto

// 岗位分页查询条件
type PostPageReq struct {
	PostCode string `form:"code"`  // 岗位编码
	PostName string `form:"name"`  // 岗位名称
	State    string `form:"state"` // 岗位状态
	PageReq
}

// 新增岗位
type PostInsertReq struct {
	PostCode string `json:"code" binding:"required"`
	PostName string `json:"name"`
	Sort     uint   `json:"sort"`
	State    string `json:"state"`
	Remark   string `json:"remark"`
}

// 更新岗位
type PostUpdateReq struct {
	PostInsertReq
}
