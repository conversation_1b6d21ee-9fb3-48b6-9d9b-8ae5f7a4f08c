package dto

// 参数配置分页查询条件
type ConfigPageReq struct {
	Name      string `form:"name"`      // 参数名称
	Key       string `form:"key"`       // 参数键名
	Type      string `form:"type"`      // 系统内置
	CreatedAt string `form:"createdAt"` // 创建时间
	PageReq
}

// 新增参数配置
type ConfigInsertReq struct {
	Name   string `json:"name" binding:"required"`
	Key    string `json:"key" binding:"required"`
	Value  string `json:"value" binding:"required"`
	Type   string `json:"type"`
	Remark string `json:"remark"`
}

// 更新参数配置
type ConfigUpdateReq struct {
	// ID int `form:"id" binding:"required"`
	ConfigInsertReq
}
