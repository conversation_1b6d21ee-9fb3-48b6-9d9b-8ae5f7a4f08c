package dto

// 用户分页查询条件
type UserPageReq struct {
	UserName  string `form:"userName"`  // 登录名称
	Mobile    string `form:"mobile"`    // 手机号码
	State     string `form:"state"`     // 用户状态
	CreatedAt string `form:"createdAt"` // 创建时间
	DeptID    uint   `form:"deptId"`    // 部门
	PageReq
}

// 新增用户
type UserInsertReq struct {
	UserUpdateReq
	Password string `json:"password" binding:"required"`
}

// 更新用户
type UserUpdateReq struct {
	UserName string  `json:"userName" binding:"required,min=3,max=20"`
	NickName string  `json:"nickName"`
	RealName string  `json:"realName"`
	DeptID   *uint   `json:"deptId"`
	Mobile   string  `json:"mobile"`
	Email    string  `json:"email"`
	Gender   string  `json:"gender"`
	State    string  `json:"state"`
	PostIds  *string `json:"postIds"` // 用户岗位
	RoleId   *uint   `json:"roleId"`  // 用户角色
	Remark   string  `json:"remark"`
}

// 修改密码
type UserUpdatePwdReq struct {
	OldPassword string `json:"oldPassword" binding:"required"`
	NewPassword string `json:"newPassword" binding:"required"`
	RePassword  string `json:"rePassword" binding:"required"`
}

// 重置密码
type UserResetPwdReq struct {
	Password   string `json:"password" binding:"required"`
	RePassword string `json:"rePassword" binding:"required"`
}

// 检查用户
type UserCheckReq struct {
	UserName string `form:"userName" binding:"required"` // 登录名称
}
