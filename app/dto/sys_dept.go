package dto

// 部门查询条件
type DeptReq struct {
	DeptName string `form:"name"`  // 部门名称
	State    string `form:"state"` // 部门状态
}

// 新增部门
type DeptInsertReq struct {
	DeptPid  uint    `json:"deptPid"`
	DeptCode string  `json:"code" binging:"required"`
	DeptName string  `json:"name"`
	Sort     uint    `json:"sort"`
	Leader   *string `json:"leader"`
	State    string  `json:"state"`
}

// 更新部门
type DeptUpdateReq struct {
	DeptInsertReq
}
