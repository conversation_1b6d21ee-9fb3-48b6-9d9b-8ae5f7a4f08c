package dto

// 通知公告分页查询条件
type NoticePageReq struct {
	Title     string `form:"title"`     // 公告标题
	Type      string `form:"type"`      // 公告类型
	State     string `form:"state"`     // 公告状态
	CreatedBy string `form:"createdBy"` // 创建者
	CreatedAt string `form:"createdAt"` // 创建时间
	PageReq
}

// 新增通知公告
type NoticeInsertReq struct {
	Title   string `json:"title" binding:"required"`
	Type    string `json:"type"`
	State   string `json:"state"`
	IsTop   string `json:"isTop"`
	Remark  string `json:"remark"`
	Content string `json:"content"`
}

// 更新通知公告
type NoticeUpdateReq struct {
	// ID int `form:"id" binding:"required"`
	NoticeInsertReq
}
