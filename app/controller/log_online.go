// Package controller 在线用户
package controller

import (
	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/service"
	"lsservice/utils/response"

	"github.com/gin-gonic/gin"
)

// LogOnline 在线用户
var LogOnline = new(logOnlineCtl)

type logOnlineCtl struct{}

// 1.在线用户列表
func (loc *logOnlineCtl) List(ctx *gin.Context) {
	// 参数
	var req *dto.LogOnlinePageReq
	// if req == nil {
	// 	response.Fail(ctx, consts.ValidatorParamsCheckFailCode, consts.ValidatorParamsCheckFailMsg, "")
	// 	return
	// }
	if err := ctx.BindQuery(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	// 调用获取列表方法
	count, temp, err := service.LogOnline.GetList(req)
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.SuccessByPage(ctx, consts.CurdStatusOkMsg, count, temp)
}

// 2.在线用户下线
func (loc *logOnlineCtl) Offline(ctx *gin.Context) {
	// ID
	var uri dto.UriID
	if err := ctx.BindUri(&uri); err != nil {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	if err := service.LogOnline.OffLine(&uri.ID); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 3.清空离线用户
func (loc *logOnlineCtl) Empty(ctx *gin.Context) {
	if err := service.LogOnline.Empty(); err != nil {
		response.Fail(ctx, consts.CurdDeleteFailCode, consts.CurdDeleteFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}
