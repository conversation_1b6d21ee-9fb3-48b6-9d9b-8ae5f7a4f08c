// Package controller 上传文件
package controller

import (
	"lsservice/app/global/consts"
	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
	"lsservice/app/service"
	"lsservice/utils/response"

	"github.com/gin-gonic/gin"
)

// Upload 上传文件
var Upload = new(uploadCtl)

type uploadCtl struct{}

// 1.上传图片
func (uc *uploadCtl) UploadImg(ctx *gin.Context) {
	savePath := variable.BasePath + paths.UploadFileSavePath

	errno, finnalSavePath, _ := service.Upload.UploadImg(ctx, savePath)
	if errno > 0 {
		response.ImageResponse(ctx, consts.FilesUploadFailCode, consts.FilesUploadFailMsg, errno, nil)
		return
	}

	response.ImageResponse(ctx, consts.CurdStatusOkCode, consts.CurdStatusOkMsg, 0, finnalSavePath)
}

// 2.上传文件
func (uc *uploadCtl) UploadFile(ctx *gin.Context) {
	savePath := variable.BasePath + paths.UploadFileSavePath

	finnalSavePath, err := service.Upload.UploadFile(ctx, savePath)
	if err != nil {
		response.Fail(ctx, consts.FilesUploadFailCode, consts.FilesUploadFailMsg, err.Error())
		return
	}

	response.Success(ctx, consts.CurdStatusOkMsg, finnalSavePath)
}
