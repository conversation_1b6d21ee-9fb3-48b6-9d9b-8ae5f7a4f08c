package controller

import (
	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/service"
	"lsservice/utils/gconv"
	"lsservice/utils/response"

	"github.com/gin-gonic/gin"
)

// Config 参数配置
var Config = new(configCtl)

type configCtl struct{}

// 1.参数配置列表(List)
func (cc *configCtl) List(ctx *gin.Context) {
	// 参数
	var req *dto.ConfigPageReq
	if err := ctx.BindQuery(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	// 调用获取列表方法
	count, temp, err := service.Config.GetList(req)
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.SuccessByPage(ctx, consts.CurdStatusOkMsg, count, temp)
}

// 2.参数配置详情(Detail)
func (cc *configCtl) Detail(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	temp, err := service.Config.GetOne(gconv.Uint(id))
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}

// 3.新增参数配置(Insert)
func (cc *configCtl) Insert(ctx *gin.Context) {
	// 参数
	var req *dto.ConfigInsertReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	id, err := service.Config.Insert(ctx, req)
	if err != nil {
		response.Fail(ctx, consts.CurdCreatFailCode, consts.CurdCreatFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, id)
}

// 4.更新参数配置(Update)
func (cc *configCtl) Update(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.ConfigUpdateReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.Config.Update(ctx, req, gconv.Uint(id)); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 5.删除参数配置(Delete)
func (cc *configCtl) Delete(ctx *gin.Context) {
	ids := ctx.Param("ids")
	if ids == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	num, err := service.Config.Delete(ids)
	if err != nil {
		response.Fail(ctx, consts.CurdDeleteFailCode, consts.CurdDeleteFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, num)
}

// 6.参数配置导出(Export)
func (cc *configCtl) Export(ctx *gin.Context) {
	// 参数
	var req *dto.ExportReq
	if err := ctx.BindQuery(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	downURL, err := service.Config.ExportCSV(req)
	if err != nil {
		response.Fail(ctx, consts.CurdExportFailCode, consts.CurdExportFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, downURL)
}
