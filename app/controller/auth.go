// Package controller 登录认证
package controller

import (
	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/service"
	"lsservice/utils/response"

	"github.com/gin-gonic/gin"
)

// Auth 登录认证
var Auth = new(authCtl)

type authCtl struct{}

// 1.用户登录
func (ac *authCtl) Login(ctx *gin.Context) {
	var req *dto.UserLoginReq

	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	// 登录
	user, err := service.Auth.Login(ctx, req)
	if err != nil {
		response.Fail(ctx, consts.CurdLoginFailCode, consts.CurdLoginFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, user)
}

// 2.用户登出
func (ac *authCtl) Logout(ctx *gin.Context) {
	if err := service.Auth.Logout(ctx); err != nil {
		response.Fail(ctx, consts.CurdLogoutFailCode, consts.CurdLogoutFailMsg, err.Error())
		return
	}

	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 3.获取验证码
func (ac *authCtl) Captcha(ctx *gin.Context) {
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 4.检查用户是否已经登录
func (ac *authCtl) LoginCheck(ctx *gin.Context) {
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}
