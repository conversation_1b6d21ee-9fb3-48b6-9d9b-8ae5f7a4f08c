// Package controller 岗位管理
package controller

import (
	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/service"
	"lsservice/utils/gconv"
	"lsservice/utils/response"

	"github.com/gin-gonic/gin"
)

// Post 岗位管理
var Post = new(postCtl)

type postCtl struct{}

// 1.岗位列表(List)
func (pc *postCtl) List(ctx *gin.Context) {
	// 参数
	var req *dto.PostPageReq
	if err := ctx.BindQuery(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	// 调用获取列表方法
	count, temp, err := service.Post.GetList(req)
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.SuccessByPage(ctx, consts.CurdStatusOkMsg, count, temp)
}

// 2.岗位详情(Detail)
func (pc *postCtl) Detail(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	temp, err := service.Post.GetOne(gconv.Uint(id))
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}

// 3.新增岗位(Insert)
func (pc *postCtl) Insert(ctx *gin.Context) {
	// 参数
	var req *dto.PostInsertReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	id, err := service.Post.Insert(ctx, req)
	if err != nil {
		response.Fail(ctx, consts.CurdCreatFailCode, consts.CurdCreatFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, id)
}

// 4.更新岗位(Update)
func (pc *postCtl) Update(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.PostUpdateReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.Post.Update(ctx, req, gconv.Uint(id)); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 5.删除岗位(Delete)
func (pc *postCtl) Delete(ctx *gin.Context) {
	ids := ctx.Param("ids")
	if ids == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	num, err := service.Post.Delete(ids)
	if err != nil {
		response.Fail(ctx, consts.CurdDeleteFailCode, consts.CurdDeleteFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, num)
}

// 6.岗位状态更改
func (pc *postCtl) State(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.StateReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.Post.State(req, gconv.Uint(id)); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 7.岗位列表(ListSimple)
func (pc *postCtl) ListSimple(ctx *gin.Context) {
	// 调用获取列表方法
	temp, err := service.Post.GetListSimple()
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}

// 8.岗位导出(Export)
func (pc *postCtl) Export(ctx *gin.Context) {
	// 参数
	var req *dto.ExportReq
	if err := ctx.BindQuery(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	downURL, err := service.Post.ExportCSV(req)
	if err != nil {
		response.Fail(ctx, consts.CurdExportFailCode, consts.CurdExportFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, downURL)
}
