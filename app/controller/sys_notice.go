// Package controller 通知公告
package controller

import (
	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/service"
	"lsservice/utils/response"

	"github.com/gin-gonic/gin"
)

// Notice 通知公告
var Notice = new(noticeCtl)

type noticeCtl struct{}

// 1.通知公告列表(List)
func (nc *noticeCtl) List(ctx *gin.Context) {
	// 参数
	var req *dto.NoticePageReq
	if err := ctx.ShouldBind(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	// 调用获取列表方法
	count, temp, err := service.Notice.GetList(req)
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.SuccessByPage(ctx, consts.CurdStatusOkMsg, count, temp)
}

// 2.通知公告详情(Detail)
func (nc *noticeCtl) Detail(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	temp, err := service.Notice.GetOne(id)
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}

// 3.新增通知公告(Insert)
func (nc *noticeCtl) Insert(ctx *gin.Context) {
	// 参数
	var req *dto.NoticeInsertReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	id, err := service.Notice.Insert(ctx, req)
	if err != nil {
		response.Fail(ctx, consts.CurdCreatFailCode, consts.CurdCreatFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, id)
}

// 4.更新通知公告(Update)
func (nc *noticeCtl) Update(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.NoticeUpdateReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.Notice.Update(ctx, req, id); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 5.删除通知公告(Delete)
func (nc *noticeCtl) Delete(ctx *gin.Context) {
	ids := ctx.Param("ids")
	if ids == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	num, err := service.Notice.Delete(ids)
	if err != nil {
		response.Fail(ctx, consts.CurdDeleteFailCode, consts.CurdDeleteFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, num)
}

// 6.通知公告状态更改
func (nc *noticeCtl) State(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.StateReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.Notice.State(req, id); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}
