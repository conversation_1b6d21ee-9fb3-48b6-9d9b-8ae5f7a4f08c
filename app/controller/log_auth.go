// Package controller 认证日志
package controller

import (
	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/service"
	"lsservice/utils/gconv"
	"lsservice/utils/response"

	"github.com/gin-gonic/gin"
)

// LogAuth 认证日志
var LogAuth = new(logAuthCtl)

type logAuthCtl struct{}

// 1.访问日志列表(List)
func (lac *logAuthCtl) List(ctx *gin.Context) {
	// 参数
	var req *dto.LogAuthPageReq
	if err := ctx.ShouldBind(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	// 调用获取列表方法
	count, temp, err := service.LogAuth.GetList(req)
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.<PERSON>rror())
		return
	}
	response.SuccessByPage(ctx, consts.CurdStatusOkMsg, count, temp)
}

// 2.访问日志详情(Detail)
func (lac *logAuthCtl) Detail(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	temp, err := service.LogAuth.GetOne(gconv.Uint(id))
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}

// 3.删除访问日志(Delete)
func (lac *logAuthCtl) Delete(ctx *gin.Context) {
	ids := ctx.Param("ids")
	if ids == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	if err := service.LogAuth.Delete(ids); err != nil {
		response.Fail(ctx, consts.CurdDeleteFailCode, consts.CurdDeleteFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 4.清空访问日志(Empty)
func (lac *logAuthCtl) Empty(ctx *gin.Context) {
	if err := service.LogAuth.Empty(); err != nil {
		response.Fail(ctx, consts.CurdDeleteFailCode, consts.CurdDeleteFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 5.访问日志导出CSV
func (lac *logAuthCtl) Export(ctx *gin.Context) {
	// 参数
	var req *dto.ExportReq
	if err := ctx.ShouldBind(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	downURL, err := service.LogAuth.ExportCSV(req)
	if err != nil {
		response.Fail(ctx, consts.CurdExportFailCode, consts.CurdExportFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, downURL)
}
