// Package controller 服务器信息
package controller

import (
	"lsservice/app/global/consts"
	"lsservice/app/service"
	"lsservice/utils/response"

	"github.com/gin-gonic/gin"
)

// Server 服务器信息
var Server = new(serverCtl)

type serverCtl struct{}

// 1.获取服务器信息
func (s *serverCtl) Info(ctx *gin.Context) {
	temp, err := service.Server.GetInfo()
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}
