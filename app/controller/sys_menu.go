// Package controller 菜单管理
package controller

import (
	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/service"
	"lsservice/utils/gconv"
	"lsservice/utils/response"

	"github.com/gin-gonic/gin"
)

// Menu 菜单管理
var Menu = new(menuCtl)

type menuCtl struct{}

// 1.菜单树(Tree)
func (mc *menuCtl) Tree(ctx *gin.Context) {
	// 参数
	var req *dto.MenuReq
	query := ctx.Request.URL.Query()
	if len(query) != 0 {
		if err := ctx.BindQuery(&req); err != nil {
			response.ErrorParam(ctx, err.Error())
			return
		}
	}

	// 调用获取树方法
	temp, err := service.Menu.GetTree(req)
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}

// 2.菜单详情(Detail)
func (mc *menuCtl) Detail(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	temp, err := service.Menu.GetOne(gconv.Uint(id))
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}

// 3.新增菜单(Insert)
func (mc *menuCtl) Insert(ctx *gin.Context) {
	// 参数
	var req *dto.MenuInsertReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	id, err := service.Menu.Insert(ctx, req)
	if err != nil {
		response.Fail(ctx, consts.CurdCreatFailCode, consts.CurdCreatFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, id)
}

// 4.更新菜单(Update)
func (mc *menuCtl) Update(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.MenuUpdateReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.Menu.Update(ctx, req, gconv.Uint(id)); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 5.删除菜单(Delete)
func (mc *menuCtl) Delete(ctx *gin.Context) {
	ids := ctx.Param("ids")
	if ids == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	num, err := service.Menu.Delete(ids)
	if err != nil {
		response.Fail(ctx, consts.CurdDeleteFailCode, consts.CurdDeleteFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, num)
}

// 6.菜单显示状态更改
func (mc *menuCtl) Visible(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.MenuVisibleReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.Menu.Visible(req, gconv.Uint(id)); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 7.选择树(TreeSimple)
func (mc *menuCtl) TreeSimple(ctx *gin.Context) {
	// 调用获取树方法
	temp, err := service.Menu.GetTreeSimple()
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}

// 8.根据角色权限获取菜单
func (mc *menuCtl) TreeByRole(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 调用获取树方法
	temp, err := service.Menu.GetMenuByRole(gconv.Uint(id))
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}
