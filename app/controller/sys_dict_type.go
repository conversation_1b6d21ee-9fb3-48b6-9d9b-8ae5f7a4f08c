// Package controller 字典类型
package controller

import (
	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/service"
	"lsservice/utils/gconv"
	"lsservice/utils/response"

	"github.com/gin-gonic/gin"
)

// DictType 字典类型
var DictType = new(dictTypeCtl)

type dictTypeCtl struct{}

// 1.字典类型列表(List)
func (dtc *dictTypeCtl) List(ctx *gin.Context) {
	// 参数
	var req *dto.DictTypePageReq
	if err := ctx.BindQuery(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	// 调用获取列表方法
	count, temp, err := service.DictType.GetList(req)
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.SuccessByPage(ctx, consts.CurdStatusOkMsg, count, temp)
}

// 2.字典类型详情(Detail)
func (dtc *dictTypeCtl) Detail(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	temp, err := service.DictType.GetOne(gconv.Uint(id))
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}

// 3.新增字典类型(Insert)
func (dtc *dictTypeCtl) Insert(ctx *gin.Context) {
	// 参数
	var req *dto.DictTypeInsertReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	id, err := service.DictType.Insert(ctx, req)
	if err != nil {
		response.Fail(ctx, consts.CurdCreatFailCode, consts.CurdCreatFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, id)
}

// 4.更新字典类型(Update)
func (dtc *dictTypeCtl) Update(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.DictTypeUpdateReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.DictType.Update(ctx, req, gconv.Uint(id)); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 5.删除字典类型(Delete)
func (dtc *dictTypeCtl) Delete(ctx *gin.Context) {
	ids := ctx.Param("ids")
	if ids == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	num, err := service.DictType.Delete(ids)
	if err != nil {
		response.Fail(ctx, consts.CurdDeleteFailCode, consts.CurdDeleteFailMsg, nil)
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, num)
}

// 6.字典类型状态更改
func (dtc *dictTypeCtl) State(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.StateReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.DictType.State(req, gconv.Uint(id)); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 7.字典类型导出(Export)
func (dtc *dictTypeCtl) Export(ctx *gin.Context) {
	// 参数
	var req *dto.ExportReq
	if err := ctx.BindQuery(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	downURL, err := service.DictType.ExportCSV(req)
	if err != nil {
		response.Fail(ctx, consts.CurdExportFailCode, consts.CurdExportFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, downURL)
}
