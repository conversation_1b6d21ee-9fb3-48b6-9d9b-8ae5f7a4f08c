// Package controller 用户管理
package controller

import (
	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/service"
	"lsservice/utils/gconv"
	"lsservice/utils/response"

	"github.com/gin-gonic/gin"
)

// User 用户管理
var User = new(userCtl)

type userCtl struct{}

// 3.用户列表(List)
func (uc *userCtl) List(ctx *gin.Context) {
	// 参数
	var req *dto.UserPageReq
	// if req == nil {
	// 	response.Fail(ctx, consts.ValidatorParamsCheckFailCode, consts.ValidatorParamsCheckFailMsg, "")
	// 	return
	// }
	if err := ctx.BindQuery(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	// 调用获取列表方法
	count, temp, err := service.User.GetList(req)
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.SuccessByPage(ctx, consts.CurdStatusOkMsg, count, temp)
}

// 4.用户详情(Detail)
func (uc *userCtl) Detail(ctx *gin.Context) {
	// ID
	var uri dto.UriID
	if err := ctx.BindUri(&uri); err != nil {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}
	// id := ctx.Param("id")
	// if id == "" {
	// 	response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
	// 	return
	// }

	temp, err := service.User.GetOne(gconv.Uint(&uri.ID))
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}

// 5.新增用户(Insert)
func (uc *userCtl) Insert(ctx *gin.Context) {
	// 参数
	var req *dto.UserInsertReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	id, err := service.User.Insert(ctx, req)
	if err != nil {
		response.Fail(ctx, consts.CurdCreatFailCode, consts.CurdCreatFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, id)
}

// 6.更新用户(Update)
func (uc *userCtl) Update(ctx *gin.Context) {
	// ID
	var uri dto.UriID
	if err := ctx.BindUri(&uri); err != nil {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.UserUpdateReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.User.Update(ctx, req, gconv.Uint(&uri.ID)); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 7.删除用户(Delete)
func (uc *userCtl) Delete(ctx *gin.Context) {
	// IDs
	var uri dto.UriIDs
	if err := ctx.BindUri(&uri); err != nil {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	num, err := service.User.Delete(uri.IDs)
	if err != nil {
		response.Fail(ctx, consts.CurdDeleteFailCode, consts.CurdDeleteFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, num)
}

// 8.用户状态更改
func (uc *userCtl) State(ctx *gin.Context) {
	// ID
	var uri dto.UriID
	if err := ctx.BindUri(&uri); err != nil {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.StateReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.User.State(req, gconv.Uint(&uri.ID)); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 9.用户密码重置
func (uc *userCtl) ResetPwd(ctx *gin.Context) {
	// ID
	var uri dto.UriID
	if err := ctx.BindUri(&uri); err != nil {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.UserResetPwdReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.User.ResetPwd(req, gconv.Uint(&uri.ID)); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 10.用户密码更改
func (uc *userCtl) UpdatePwd(ctx *gin.Context) {
	// ID
	var uri dto.UriID
	if err := ctx.BindUri(&uri); err != nil {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.UserUpdatePwdReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.User.UpdatePwd(req, gconv.Uint(&uri.ID)); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 11.检查用户登录账号是否存在
func (uc *userCtl) CheckUser(ctx *gin.Context) {
	// 参数
	var req *dto.UserCheckReq
	if err := ctx.BindQuery(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if isExist := service.User.CheckUser(req.UserName); isExist {
		response.Fail(ctx, consts.CurdFailCode, consts.NameIsExist, nil)
		return
	}

	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 12.用户导入(Import)
func (uc *userCtl) Import(ctx *gin.Context) {
	response.Success(ctx, consts.CurdStatusOkMsg, "导入")
}

// 13.用户导出(Export)
func (uc *userCtl) Export(ctx *gin.Context) {
	// 参数
	var req *dto.ExportReq
	if err := ctx.BindQuery(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	downURL, err := service.User.ExportCSV(req)
	if err != nil {
		response.Fail(ctx, consts.CurdExportFailCode, consts.CurdExportFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, downURL)
}
