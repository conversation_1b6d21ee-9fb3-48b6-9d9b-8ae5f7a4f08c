// Package controller 部门管理
package controller

import (
	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/service"
	"lsservice/utils/gconv"
	"lsservice/utils/response"

	"github.com/gin-gonic/gin"
)

// Dept 部门管理
var Dept = new(deptCtl)

type deptCtl struct{}

// 1.部门树(Tree)
func (dc *deptCtl) Tree(ctx *gin.Context) {
	// 参数
	var req *dto.DeptReq
	query := ctx.Request.URL.Query()
	if len(query) != 0 {
		if err := ctx.BindQuery(&req); err != nil {
			response.ErrorParam(ctx, err.Error())
			return
		}
	}
	// log.Printf("%+v", req)

	// 调用获取树方法
	temp, err := service.Dept.GetTree(req)
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}

// 2.菜单详情(Detail)
func (dc *deptCtl) Detail(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	temp, err := service.Dept.GetOne(gconv.Uint(id))
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}

// 3.新增菜单(Insert)
func (dc *deptCtl) Insert(ctx *gin.Context) {
	// 参数
	var req *dto.DeptInsertReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	id, err := service.Dept.Insert(ctx, req)
	if err != nil {
		response.Fail(ctx, consts.CurdCreatFailCode, consts.CurdCreatFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, id)
}

// 4.更新菜单(Update)
func (dc *deptCtl) Update(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.DeptUpdateReq
	if err := ctx.BindJSON(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	if err := service.Dept.Update(ctx, req, gconv.Uint(id)); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 5.删除菜单(Delete)
func (dc *deptCtl) Delete(ctx *gin.Context) {
	ids := ctx.Param("ids")
	if ids == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	num, err := service.Dept.Delete(ids)
	if err != nil {
		response.Fail(ctx, consts.CurdDeleteFailCode, consts.CurdDeleteFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, num)
}

// 8.部门状态更改
func (dc *deptCtl) State(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.Fail(ctx, consts.CurdEmptyIdCode, consts.CurdEmptyIdMsg, nil)
		return
	}

	// 参数
	var req *dto.StateReq
	if err := ctx.BindJSON(&req); err != nil {
		response.Fail(ctx, consts.ValidatorParamsCheckFailCode, consts.ValidatorParamsCheckFailMsg, "")
		return
	}

	if err := service.Dept.State(req, gconv.Uint(id)); err != nil {
		response.Fail(ctx, consts.CurdUpdateFailCode, consts.CurdUpdateFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, nil)
}

// 9.选择树(TreeSimple)
func (dc *deptCtl) TreeSimple(ctx *gin.Context) {
	// 调用获取树方法
	temp, err := service.Dept.GetTreeSimple()
	if err != nil {
		response.Fail(ctx, consts.CurdSelectFailCode, consts.CurdSelectFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, temp)
}

// 10.部门导出(Export)
func (dc *deptCtl) Export(ctx *gin.Context) {
	// 参数
	var req *dto.ExportReq
	if err := ctx.BindQuery(&req); err != nil {
		response.ErrorParam(ctx, err.Error())
		return
	}

	downURL, err := service.Dept.ExportCSV(req)
	if err != nil {
		response.Fail(ctx, consts.CurdExportFailCode, consts.CurdExportFailMsg, err.Error())
		return
	}
	response.Success(ctx, consts.CurdStatusOkMsg, downURL)
}
