package consts

// 这里定义的常量，一般是具有错误代码+错误说明组成，一般用于接口返回
const (
	// 进程被结束
	ProcessKilled string = "收到信号，进程被结束"
	// 表单验证器前缀
	ValidatorPrefix              string = "Form_Validator_"
	ValidatorParamsCheckFailCode int    = -400300
	ValidatorParamsCheckFailMsg  string = "参数校验失败"

	// 服务器代码发生错误
	ServerOccurredErrorCode int    = -500100
	ServerOccurredErrorMsg  string = "服务器内部发生代码执行错误"
	GinSetTrustProxyError   string = "Gin 设置信任代理服务器出错"

	// token相关
	JwtTokenOK            int    = 200100                      // token有效
	JwtTokenInvalid       int    = -400100                     // 无效的token
	JwtTokenExpired       int    = -400101                     // 过期的token
	JwtTokenFormatErrCode int    = -400102                     // 提交的 token 格式错误
	JwtTokenFormatErrMsg  string = "提交的 token 格式错误"            // 提交的 token 格式错误
	JwtTokenMustValid     string = "token为必填项,请在请求header部分提交!" // 提交的 token 格式错误

	// cookie相关
	CookieServiceName      string = "lsservice"
	CookieWebsiteName      string = "TIWEB"
	SessionErrorCode       int    = -400301
	SessionErrorMsg        string = "Session 错误"
	SessionNotFound        string = "Session 不存在"
	SessionCheckError      string = "Session 检验失败"
	SessionUpdateDateError string = "Session 时间更新出错"

	// SnowFlake 雪花算法
	StartTimeStamp = int64(1483228800000) // 开始时间截 (2017-01-01)
	MachineIdBits  = uint(10)             // 机器id所占的位数
	SequenceBits   = uint(12)             // 序列所占的位数
	// MachineIdMax   = int64(-1 ^ (-1 << MachineIdBits)) //支持的最大机器id数量
	SequenceMask   = int64(-1 ^ (-1 << SequenceBits)) //
	MachineIdShift = SequenceBits                     // 机器id左移位数
	TimestampShift = SequenceBits + MachineIdBits     // 时间戳左移位数

	// CURD 常用业务状态码
	CurdStatusOkCode         int    = 200
	CurdStatusOkMsg          string = "成功"
	CurdCreatFailCode        int    = -400200
	CurdCreatFailMsg         string = "新增失败"
	CurdUpdateFailCode       int    = -400201
	CurdUpdateFailMsg        string = "更新失败"
	CurdDeleteFailCode       int    = -400202
	CurdDeleteFailMsg        string = "删除失败"
	CurdSelectFailCode       int    = -400203
	CurdSelectFailMsg        string = "查询无数据"
	CurdRegisterFailCode     int    = -400204
	CurdRegisterFailMsg      string = "注册失败"
	CurdLoginFailCode        int    = -400205
	CurdLoginFailMsg         string = "登录失败"
	CurdRefreshTokenFailCode int    = -400206
	CurdRefreshTokenFailMsg  string = "刷新Token失败"
	CurdTokenFailCode        int    = -400207
	CurdTokenFailMsg         string = "token错误"

	CurdEmptyIdCode    int    = -400208
	CurdEmptyIdMsg     string = "ID不能为空"
	CurdEmptyNameCode  int    = -400209
	CurdEmptyNameMsg   string = "Name不能为空"
	CurdLogoutFailCode int    = -400210
	CurdLogoutFailMsg  string = "登出失败"

	CurdImportFailCode int    = -400211
	CurdImportFailMsg  string = "导入失败"
	CurdExportFailCode int    = -400212
	CurdExportFailMsg  string = "导出失败"

	CurdFailCode int    = 400
	IDNoExist    string = "ID不存在"
	NoData       string = "无数据"
	NameIsExist  string = "账号已存在"
	PwdEncrypt   string = "密码保存失败"

	// 文件上传
	FilesUploadFailCode            int    = -400250
	FilesUploadFailMsg             string = "文件上传失败, 获取上传文件发生错误!"
	FilesUploadMoreThanMaxSizeCode int    = -400251
	FilesUploadMoreThanMaxSizeMsg  string = "长传文件超过系统设定的最大值,系统允许的最大值（M）："
	FilesUploadMimeTypeFailCode    int    = -400252
	FilesUploadMimeTypeFailMsg     string = "文件mime类型不允许"

	// websocket
	WsServerNotStartCode int    = -400300
	WsServerNotStartMsg  string = "websocket 服务没有开启，请在配置文件开启，相关路径：config/config.yml"
	WsOpenFailCode       int    = -400301
	WsOpenFailMsg        string = "websocket open阶段初始化基本参数失败"

	// 验证码
	CaptchaGetParamsInvalidMsg    string = "获取验证码：提交的验证码参数无效,请检查验证码ID以及文件名后缀是否完整"
	CaptchaGetParamsInvalidCode   int    = -400350
	CaptchaCheckParamsInvalidMsg  string = "校验验证码：提交的参数无效，请检查 【验证码ID、验证码值】 提交时的键名是否与配置项一致"
	CaptchaCheckParamsInvalidCode int    = -400351
	CaptchaCheckOkMsg             string = "验证码校验通过"
	// CaptchaCheckOkCode            int    = 200
	CaptchaCheckFailCode int    = -400355
	CaptchaCheckFailMsg  string = "验证码校验失败"

	// 权限分配类
	AuthAssginOkMsg    string = "权限分配成功"
	AuthAssginFailCode int    = -400400
	AuthAssginFailMsg  string = "权限分配失败"

	AuthDelOkMsg    string = "权限删除成功"
	AuthDelFailCode int    = -400401
	AuthDelFailMsg  string = "权限删除失败"
)
