package paths

import "lsservice/app/global/variable"

const (
	// 公共路径
	PublicDir = "/public/"
	LogDir    = "/storage/logs/"
	AppDir    = "/storage/app"
	SymApp    = "/public/storage"
	// 配置文件路径
	ConfigFile = "/config/config.yml"
	Gormv2File = "/config/gorm_v2.yml"
	// 日志文件路径
	GinLogName = AppDir + "/gin.log" // 设置 gin 框架的接口访问日志
	SysLogName = AppDir + "/sys.log" // 设置项目运行时日志文件名，注意该名称不要与上一条重复 ,避免和 gin 框架的日志掺杂一起，造成混乱。
	// 上传文件路径
	UploadFileSavePath   = AppDir + "/uploaded/" // 上传文件保存在路径, 该路径与 BasePath 进行拼接使用
	UploadFileReturnPath = SymApp + "/uploaded/" // 文件上后返回的路径，由于程序会自动创建软连接，自动将资源定位到实际路径，所有资源的访问入口建议都从public开始
)

// 导出 csv 路径
func CreateCsvPath(name string) string {
	return variable.ConfigYml.GetString("IOSetting.ExportPath") + name + ".csv"
}

// 下载 csv 路径
func DownCsvPath(name string) string {
	return variable.ConfigYml.GetString("IOSetting.ReturnPath") + name + ".csv"
}
