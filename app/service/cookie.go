package service

import (
	"lsservice/app/global/consts"

	"github.com/gin-gonic/gin"
)

var Cookie = new(cookieService)

type cookieService struct{}

// 获取 cookie
func (c *cookieService) Get(ctx *gin.Context) (session string, err error) {
	cookie, err := ctx.Cookie(consts.CookieServiceName)
	if err != nil {
		return "", err
	}

	return cookie, nil
}

// 设置 cookie
func (c *cookieService) Set(ctx *gin.Context, session string) bool {
	ctx.SetCookie(consts.CookieServiceName, session, 3600, "/", "127.0.0.1", false, true)

	return true
}

// 删除 cookie
func (c *cookieService) Delete(ctx *gin.Context) bool {
	ctx.SetCookie(consts.CookieServiceName, "", -1, "/", "127.0.0.1", false, true)

	return true
}
