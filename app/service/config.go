package service

import (
	"errors"
	"os"
	"strings"

	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/utils/gconv"

	"github.com/gin-gonic/gin"
	"github.com/tushar2708/altcsv"
)

var Config = new(configService)

type configService struct{}

// 1.获取参数配置列表
func (cs *configService) GetList(req *dto.ConfigPageReq) (count int64, temp []model.Config, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql

	// 查询条件
	if req != nil {
		// 参数名称
		if req.Name != "" {
			query = query.Where("name like ?", "%"+strings.TrimSpace(req.Name)+"%")
		}
		// 参数键名
		if req.Key != "" {
			query = query.Where("key like ?", "%"+strings.TrimSpace(req.Key)+"%")
		}
		// 系统内置
		if req.Type != "" {
			query = query.Where("type = ?", req.Type)
		}
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}
		}
	}

	if query.Model(&temp).Count(&count); count == 0 {
		return 0, nil, nil
	}

	// 排序
	if req.SortBy != "" {
		query = query.Order(req.SortBy + " " + req.Order)
	} else {
		// 默认更新时间倒序
		query = query.Order("created_at DESC")
	}

	// 分页设置
	if req.Page != nil {
		query = query.Scopes(Paginate(&req.PageReq))
	}

	// 查询列表
	if resultErr := query.Find(&temp).Error; resultErr != nil {
		return 0, nil, resultErr
	}
	return count, temp, nil
}

// 2.获取参数配置详情
func (cs *configService) GetOne(id uint) (temp *model.Config, err error) {
	var entity model.Config
	// 检查 id 是否存在
	if !cs.IdIsExist(&entity, id) {
		return nil, errors.New(consts.IDNoExist)
	}

	return &entity, nil
}

// 3.新增参数配置
func (cs *configService) Insert(ctx *gin.Context, req *dto.ConfigInsertReq) (id *uint, err error) {
	// 检查参数键名是否存在
	if cs.CheckKey(req.Key) {
		return nil, errors.New("已存在")
	}

	var entity model.Config
	entity.ConfigName = req.Name
	entity.ConfigKey = req.Key
	entity.ConfigValue = req.Value
	entity.Remark = req.Remark

	// 写入创建者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.CreatedBy = userName
		entity.UpdatedBy = userName
	}

	if resultErr := variable.GormDbPostgreSql.Create(&entity).Error; resultErr != nil {
		return nil, resultErr
	}
	return &entity.ConfigID, nil
}

// 4.更新参数配置
func (cs *configService) Update(ctx *gin.Context, req *dto.ConfigUpdateReq, id uint) (err error) {
	var entity model.Config
	// 检查 id 是否存在
	if !cs.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 检查 config_key 是否存在
	if keyExist := variable.GormDbPostgreSql.Where("config_key = ?", req.Key).Not("config_id = ?", id).Find(&model.Config{}); keyExist.RowsAffected > 0 {
		return errors.New("参数键名已存在")
	}

	entity.ConfigName = req.Name
	entity.ConfigKey = req.Key
	entity.ConfigValue = req.Value
	entity.Remark = req.Remark

	// 写入更新者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.UpdatedBy = userName
	}

	if resultErr := variable.GormDbPostgreSql.Save(&entity).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 5.删除参数配置
func (cs *configService) Delete(ids string) (num int64, err error) {
	idArr := strings.Split(ids, ",")
	result := variable.GormDbPostgreSql.Unscoped().Delete(&[]model.Config{}, idArr)

	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

// 6.检查参数键名是否存在
func (cs *configService) CheckKey(key string) bool {
	if exist := variable.GormDbPostgreSql.Take(&model.Config{}, "config_key = ?", key); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 7.检测参数 id 是否存在
func (cs *configService) IdIsExist(config *model.Config, id uint) bool {
	if exist := variable.GormDbPostgreSql.Take(&config, id); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 8.参数配置导出CSV
func (cs *configService) ExportCSV(req *dto.ExportReq) (downUrl string, err error) {
	headers := []string{"id", "key", "name", "value", "createdby", "createdat", "updatedby", "updatedat", "remark"}

	// 初始化查询实例
	query := variable.GormDbPostgreSql

	csvName := "sys_config"

	// 查询条件
	if req != nil {
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}

			csvName = csvName + "_" + req.CreatedAt
		} else {
			csvName = csvName + "_all"
		}
	}

	// 新建csv文件
	createFile, createErr := os.Create(paths.CreateCsvPath(csvName))
	if createErr != nil {
		return "", createErr
	}
	defer createFile.Close()

	// 获取需要写入的数据
	var entity []*model.Config
	if resultErr := query.Find(&entity).Error; resultErr != nil {
		return "", resultErr
	}

	writer := altcsv.NewWriter(createFile)
	writer.AllQuotes = true
	defer writer.Flush()

	writer.Write(headers)

	for _, item := range entity {
		line := []string{
			gconv.String(item.ConfigID),
			item.ConfigKey,
			item.ConfigName,
			item.ConfigValue,
			item.CreatedBy,
			(item.CreatedAt).Format(variable.DateFormatFull),
			item.UpdatedBy,
			(item.UpdatedAt).Format(variable.DateFormatFull),
			item.Remark,
		}

		writer.Write(line)
	}

	return paths.DownCsvPath(csvName), nil
}
