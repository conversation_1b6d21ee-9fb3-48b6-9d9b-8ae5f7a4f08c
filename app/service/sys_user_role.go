package service

import (
	"fmt"

	"lsservice/app/global/variable"
	"lsservice/app/model"
)

// 用户和角色关系: 用户N-1角色
var UserRole = new(userRoleService)

type userRoleService struct{}

// 1. 根据用户ID获取角色ID
func (urs *userRoleService) GetRoleByUser(id uint) *uint {
	// 初始化查询实例
	query := variable.GormDbPostgreSql.Where("user_id = ?", id)

	item := model.UserRole{}
	if result := query.First(&item); result.Error != nil {
		return nil
	}

	return &item.RoleID
}

// 2.新增用户与角色
func (urs *userRoleService) Insert(userId uint, roleId uint) error {
	var entity model.UserRole

	entity.UserID = userId
	entity.RoleID = roleId

	result := variable.GormDbPostgreSql.Create(&entity)

	fmt.Printf("%+v\n", result)

	if result.Error != nil {
		return result.Error
	}

	return nil
}

// 3.删除用户与角色
func (urs *userRoleService) Delete(userId uint) error {
	result := variable.GormDbPostgreSql.Where("user_id = ?", userId).Delete(&model.UserRole{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// 4.删除所有用户与角色
func (urs *userRoleService) DeleteAll(userIds []string) error {
	result := variable.GormDbPostgreSql.Where("user_id IN ?", userIds).Delete(&model.UserRole{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}
