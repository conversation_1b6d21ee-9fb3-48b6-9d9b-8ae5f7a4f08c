package service

import (
	"lsservice/app/dto"

	"gorm.io/gorm"
)

func Paginate(req *dto.PageReq) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		// log.Println(req)
		page := *req.Page
		if page == 0 {
			page = 1
		}

		offset := (page - 1) * req.Limit
		return db.Limit(req.Limit).Offset(offset)
	}
}

func CalcIndex(req *dto.PageReq, idx int) int {
	return (*req.Page-1)*req.Limit + idx + 1
}
