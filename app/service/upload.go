package service

import (
	"fmt"
	"os"
	"path"
	"strings"

	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
	"lsservice/utils/md5_encrypt"

	"github.com/gin-gonic/gin"
)

var Upload = new(uploadService)

type uploadService struct{}

// 1.图片上传
func (us *uploadService) UploadImg(ctx *gin.Context, savePath string) (errno int, finnalSavePath interface{}, err error) {
	newSavePath, newRetrunPath := generatePath(savePath, "imgs_tmp")

	// fmt.Printf(newSavePath, newRetrunPath)

	// 获取上传的文件名
	file, err := ctx.FormFile(variable.ConfigYml.GetString("FileUploadSetting.UploadFileField")) //  file 是一个文件结构体（文件对象）
	if err != nil {
		return 1, nil, err
	}

	sequence := variable.SnowFlakeNode.Generate().Int64()
	saveFileName := fmt.Sprintf("%d%s", sequence, file.Filename)
	saveFileName = md5_encrypt.MD5(saveFileName) + path.Ext(saveFileName)

	// fmt.Print(saveFileName)
	if saveErr := ctx.SaveUploadedFile(file, newSavePath+saveFileName); saveErr != nil {
		return 1, nil, saveErr
	}

	finnalSavePath = gin.H{
		"url":  strings.ReplaceAll(newRetrunPath+saveFileName, variable.BasePath, ""),
		"alt":  file.Filename,
		"href": "",
	}
	return 0, finnalSavePath, nil
}

// 2. 文件上传
func (us *uploadService) UploadFile(ctx *gin.Context, savePath string) (finnalSavePath interface{}, err error) {
	newSavePath, newRetrunPath := generatePath(savePath, "files")

	// 获取上传的文件名
	file, err := ctx.FormFile(variable.ConfigYml.GetString("FileUploadSetting.UploadFileField")) //  file 是一个文件结构体（文件对象）
	if err != nil {
		return nil, err
	}

	// fmt.Printf("%+v", file)

	filenameAll := file.Filename
	fileSuffix := path.Ext(filenameAll)
	filePrefix := filenameAll[0 : len(filenameAll)-len(fileSuffix)]

	sequence := variable.SnowFlakeNode.Generate().Int64()
	saveFileName := fmt.Sprintf("%s_%d", filePrefix, sequence)
	saveFileName = saveFileName + fileSuffix

	if saveErr := ctx.SaveUploadedFile(file, newSavePath+saveFileName); saveErr != nil {
		return nil, saveErr
	}

	finnalSavePath = strings.ReplaceAll(newRetrunPath+saveFileName, variable.BasePath, "")

	return finnalSavePath, nil
}

// 文件上传可以设置按照 xxx年-xx月 格式存储
// func generateYearMonthPath(savePathPre string) (string, string) {
// 	returnPath := variable.BasePath + paths.UploadFileReturnPath
// 	curYearMonth := time.Now().Format("200601")
// 	newSavePathPre := savePathPre + curYearMonth
// 	newReturnPathPre := returnPath + curYearMonth
// 	// 相关路径不存在，创建目录
// 	if _, err := os.Stat(newSavePathPre); err != nil {
// 		if err = os.MkdirAll(newSavePathPre, os.ModePerm); err != nil {
// 			variable.ZapLog.Error("文件上传创建目录出错" + err.Error())
// 			return "", ""
// 		}
// 	}
// 	return newSavePathPre + "/", newReturnPathPre + "/"
// }

// 上传目录
func generatePath(savePathPre string, affixPath string) (string, string) {
	returnPath := variable.BasePath + paths.UploadFileReturnPath
	// curYearMonth := time.Now().Format("200601")
	newSavePathPre := savePathPre + affixPath
	newReturnPathPre := returnPath + affixPath
	// 相关路径不存在，创建目录
	if _, err := os.Stat(newSavePathPre); err != nil {
		if err = os.MkdirAll(newSavePathPre, os.ModePerm); err != nil {
			variable.ZapLog.Error("文件上传创建目录出错" + err.Error())
			return "", ""
		}
	}
	return newSavePathPre + "/", newReturnPathPre + "/"
}
