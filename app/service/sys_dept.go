package service

import (
	"errors"
	"os"
	"strings"

	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/app/vo"
	"lsservice/utils/gconv"

	"github.com/gin-gonic/gin"
	"github.com/tushar2708/altcsv"
)

var Dept = new(deptService)

type deptService struct{}

// 1.获取部门树
func (ds *deptService) GetTree(req *dto.DeptReq) (temp []*vo.DeptTreeNode, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql
	// 查询条件
	if req != nil {
		// 部门名称
		if req.DeptName != "" {
			query = query.Where("dept_name like ?", "%"+strings.TrimSpace(req.DeptName)+"%")
		}

		// 部门状态
		if req.State != "" {
			query = query.Where("state = ?", req.State)
		}
	}

	// 排序
	query = query.Order("sort")

	list := []model.Dept{}
	// 查询列表
	if resultErr := query.Find(&list).Error; resultErr != nil {
		return nil, resultErr
	}

	// fmt.Printf("%+v/n", list)

	var deptNode vo.DeptTreeNode
	ds.makeTree(list, &deptNode, 1)

	// jsonRes, _ := json.Marshal(deptNode.Children)
	// fmt.Println(string(jsonRes))

	// fmt.Printf("%+v/n", deptNode)

	// 搜索后不能成树的直接返回结果
	if deptNode.Children == nil && list != nil {
		for _, c := range list {
			child := &vo.DeptTreeNode{}
			child.Dept = c
			deptNode.Children = append(deptNode.Children, child)
		}
	}

	return deptNode.Children, nil
}

// 2.获取部门详情
func (ds *deptService) GetOne(id uint) (temp *model.Dept, err error) {
	var entity model.Dept
	// 检查 id 是否存在
	if !ds.IdIsExist(&entity, id) {
		return nil, errors.New(consts.IDNoExist)
	}

	return &entity, nil
}

// 3.新增部门
func (ds *deptService) Insert(ctx *gin.Context, req *dto.DeptInsertReq) (ids *uint, err error) {
	if ds.CheckDeptCode(req.DeptCode) {
		return nil, errors.New("已存在")
	}

	var entity model.Dept
	entity.DeptPid = req.DeptPid
	entity.DeptCode = req.DeptCode
	entity.DeptName = req.DeptName
	entity.Sort = req.Sort
	entity.Leader = req.Leader
	entity.State = req.State

	// 写入创建者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.CreatedBy = userName
		entity.UpdatedBy = userName
	}

	if resultErr := variable.GormDbPostgreSql.Create(&entity).Error; resultErr != nil {
		return nil, resultErr
	}
	return &entity.DeptID, nil
}

// 4.更新部门
func (ds *deptService) Update(ctx *gin.Context, req *dto.DeptUpdateReq, id uint) (err error) {
	var entity model.Dept
	// 检查 id 是否存在
	if !ds.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 检查部门编码是否存在
	if codeExist := variable.GormDbPostgreSql.Where("dept_code = ?", req.DeptCode).Not("dept_id = ?", id).Find(&model.User{}); codeExist.RowsAffected > 0 {
		return errors.New("部门编码已存在")
	}

	entity.DeptPid = req.DeptPid
	entity.DeptCode = req.DeptCode
	entity.DeptName = req.DeptName
	entity.Sort = req.Sort
	entity.Leader = req.Leader
	entity.State = req.State

	// 写入更新者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.UpdatedBy = userName
	}

	if resultErr := variable.GormDbPostgreSql.Save(&entity).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 5.删除部门
func (ds *deptService) Delete(ids string) (num int64, err error) {
	idArr := strings.Split(ids, ",")
	result := variable.GormDbPostgreSql.Delete(&[]model.Dept{}, idArr)
	// fmt.Printf("%+v\n", result)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

// 6.部门状态更改
func (ds *deptService) State(req *dto.StateReq, id uint) (err error) {
	var entity model.Dept
	// 查询 id 是否存在
	if !ds.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 更新状态
	if resultErr := variable.GormDbPostgreSql.Model(&entity).Update("state", req.State).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 7.检查部门编码是否存在
func (ds *deptService) CheckDeptCode(code string) bool {
	if exist := variable.GormDbPostgreSql.Where("dept_code = ?", code).Find(&model.Dept{}); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 8.检测部门 id 是否存在
func (u *deptService) IdIsExist(dept *model.Dept, id uint) bool {
	if exist := variable.GormDbPostgreSql.Find(&dept, id); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 9.获取部门树状列表
func (ds *deptService) GetTreeSimple() (temp []*vo.TreeSimple, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql

	// 部门状态为正常
	query = query.Where("state = ?", "0")

	// 排序
	query = query.Order("sort")

	list := []model.Dept{}
	// 查询列表
	if resultErr := query.Find(&list).Error; resultErr != nil {
		return nil, resultErr
	}

	// fmt.Printf("%+v/n", list)

	var deptSimple vo.TreeSimple
	ds.makeTreeSimple(list, &deptSimple)

	return deptSimple.Children, nil
}

// 10.根据ID查名称
func (ds *deptService) GetNameById(id uint) *string {
	var entity model.Dept
	// 查询 id 是否存在
	if !ds.IdIsExist(&entity, id) {
		return nil
	}

	return &entity.DeptName
}

// 11.递归生成分类列表
func (ds *deptService) makeTree(dept []model.Dept, tn *vo.DeptTreeNode, levelIndex uint) {
	for _, c := range dept {
		if c.DeptPid == tn.DeptID {
			child := &vo.DeptTreeNode{}
			child.Dept = c
			child.Level = levelIndex
			tn.Children = append(tn.Children, child)
			ds.makeTree(dept, child, levelIndex+1)
		}
	}
}

// 12.递归生成分类列表
func (ds *deptService) makeTreeSimple(dept []model.Dept, tn *vo.TreeSimple) {
	for _, c := range dept {
		if c.DeptPid == tn.Key {
			child := &vo.TreeSimple{}
			child.Key = c.DeptID
			child.Title = c.DeptName
			child.Value = c.DeptID

			// fmt.Printf("%+v/n", child)

			tn.Children = append(tn.Children, child)
			ds.makeTreeSimple(dept, child)
		}
	}
}

// 10.部门导出CSV
func (ds *deptService) ExportCSV(req *dto.ExportReq) (downUrl string, err error) {
	headers := []string{"id", "pid", "ancestors", "code", "name", "sort", "leader", "state", "createdby", "createdat", "updatedby", "updatedat"}

	// 初始化查询实例
	query := variable.GormDbPostgreSql

	csvName := "sys_dept"

	// 查询条件
	if req != nil {
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}

			csvName = csvName + "_" + req.CreatedAt
		} else {
			csvName = csvName + "_all"
		}
	}

	// 新建csv文件
	createFile, createErr := os.Create(paths.CreateCsvPath(csvName))
	if createErr != nil {
		return "", createErr
	}
	defer createFile.Close()

	// 获取需要写入的数据
	var entity []*model.Dept
	if resultErr := query.Find(&entity).Error; resultErr != nil {
		return "", resultErr
	}

	writer := altcsv.NewWriter(createFile)
	writer.AllQuotes = true
	defer writer.Flush()

	writer.Write(headers)

	for _, item := range entity {
		line := []string{
			gconv.String(item.DeptID),
			gconv.String(item.DeptPid),
			item.Ancestors,
			gconv.String(item.DeptCode),
			item.DeptName,
			gconv.String(item.Sort),
			*item.Leader,
			item.State,
			item.CreatedBy,
			(item.CreatedAt).Format(variable.DateFormatFull),
			item.UpdatedBy,
			(item.UpdatedAt).Format(variable.DateFormatFull),
		}

		writer.Write(line)
	}

	return paths.DownCsvPath(csvName), nil
}
