package service

import (
	"errors"
	"os"
	"strings"

	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/app/vo"
	"lsservice/utils/gconv"

	"github.com/gin-gonic/gin"
	"github.com/tushar2708/altcsv"
)

var Role = new(roleService)

type roleService struct{}

// 1.获取角色列表
func (rs *roleService) GetList(req *dto.RolePageReq) (count int64, temp []*model.Role, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql
	// 查询条件
	if req != nil {
		// 角色名称
		if req.RoleName != "" {
			query = query.Where("role_name like ?", "%"+strings.TrimSpace(req.RoleName)+"%")
		}
		// 权限字符
		if req.RoleCode != "" {
			query = query.Where("role_code like ?", "%"+strings.TrimSpace(req.RoleCode)+"%")
		}
		// 用户状态
		if req.State != "" {
			query = query.Where("state = ?", req.State)
		}
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}
		}
	}

	// 查询总条数
	if query.Model(&temp).Count(&count); count == 0 {
		return 0, nil, nil
	}

	// 排序
	query = query.Order("sort")

	// 分页设置
	if req.Page != nil {
		query = query.Scopes(Paginate(&req.PageReq))
	}

	// 查询列表
	if resultErr := query.Find(&temp).Error; resultErr != nil {
		return 0, nil, resultErr
	}

	return count, temp, nil
}

// 2.获取角色详情
func (rs *roleService) GetOne(id uint) (temp *vo.RoleInfoVo, err error) {
	// 检查 id 是否存在
	if !rs.IdIsExist(&model.Role{}, id) {
		return nil, errors.New(consts.IDNoExist)
	}

	// 初始化查询实例
	query := variable.GormDbPostgreSql.Table(model.RoleTableName + " AS r")

	query = query.Where("r.role_id = ?", id)

	query = query.Select("r.*, STRING_AGG(distinct rm.menu_id::character varying, ',') as menu_ids, STRING_AGG(distinct rd.dept_id::character varying, ',') as dept_ids")

	// Left Join
	query = query.Joins("left join "+model.RoleMenuTableName+" AS rm on rm.role_id=?", id)
	query = query.Joins("left join "+model.RoleDeptTableName+" AS rd on rd.role_id=?", id)

	query = query.Group("r.role_id")

	if result := query.Find(&temp); result.RowsAffected == 0 {
		if result.Error != nil {
			return nil, result.Error
		} else {
			return nil, errors.New(consts.NoData)
		}
	}

	return temp, nil
}

// 3.新增角色
func (rs *roleService) Insert(ctx *gin.Context, req *dto.RoleInsertReq) (id *uint, err error) {
	// 检查登录账号是否存在
	if rs.CheckRole(req.RoleCode) {
		return nil, errors.New("已存在")
	}

	var entity model.Role
	entity.RoleCode = req.RoleCode
	entity.RoleName = req.RoleName
	entity.Sort = req.Sort
	entity.State = req.State
	entity.Remark = req.Remark

	// 写入创建者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.CreatedBy = userName
		entity.UpdatedBy = userName
	}

	if resultErr := variable.GormDbPostgreSql.Create(&entity).Error; resultErr != nil {
		return nil, resultErr
	}

	// 插入菜单
	if req.MenuIDs != nil {
		RoleMenu.Insert(entity.RoleID, *req.MenuIDs)
	}

	return &entity.RoleID, nil
}

// 4.更新角色
func (rs *roleService) Update(ctx *gin.Context, req *dto.RoleUpdateReq, id uint) (err error) {
	query := variable.GormDbPostgreSql

	var entity model.Role
	// 检查 id 是否存在
	if !rs.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 检查 role_code 是否存在
	if codeExist := query.Where("role_code = ?", req.RoleCode).Not("role_id = ?", id).Find(&model.Role{}); codeExist.RowsAffected > 0 {
		return errors.New("角色编码已存在")
	}

	entity.RoleName = req.RoleName
	entity.RoleCode = req.RoleCode
	entity.Sort = req.Sort
	entity.State = req.State
	entity.Remark = req.Remark

	// 写入更新者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.UpdatedBy = userName
	}

	if resultErr := query.Save(&entity).Error; resultErr != nil {
		return resultErr
	}

	// 更新 岗位、角色
	if req.MenuIDs != nil {
		RoleMenu.Delete(entity.RoleID)
		RoleMenu.Insert(entity.RoleID, *req.MenuIDs)
	}

	return nil
}

// 5.删除角色
func (rs *roleService) Delete(ids string) (num int64, err error) {
	idArr := strings.Split(ids, ",")
	result := variable.GormDbPostgreSql.Delete(&[]model.Role{}, idArr)

	if result.Error != nil {
		return 0, result.Error
	}

	// 删除 岗位、角色
	for _, id := range idArr {
		RoleMenu.Delete(gconv.Uint(id))
		RoleMenu.Delete(gconv.Uint(id))
	}

	return result.RowsAffected, nil
}

// 6.角色状态更改
func (rs *roleService) State(req *dto.StateReq, id uint) (err error) {
	var entity model.Role
	// 查询 id 是否存在
	if !rs.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 更新状态
	if resultErr := variable.GormDbPostgreSql.Model(&entity).Update("state", req.State).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 7.角色导出

// 8.数据权限
func (rs *roleService) AuthDataScope(req *dto.RoleDateScopeReq, id uint) (err error) {
	query := variable.GormDbPostgreSql

	var entity model.Role
	// 检查 id 是否存在
	if !rs.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// entity.RoleName = req.Name
	// entity.RoleCode = req.Code
	entity.DataScope = req.DataScope

	if resultErr := query.Save(&entity).Error; resultErr != nil {
		return resultErr
	}

	// 更新 岗位、角色
	if req.DeptIDs != nil {
		RoleDept.Delete(entity.RoleID)
		RoleDept.Insert(entity.RoleID, *req.DeptIDs)
	}

	return nil
}

// 9.检查用户角色编码是否存在
func (rs *roleService) CheckRole(code string) bool {
	if exist := variable.GormDbPostgreSql.Where("role_code = ?", code).Find(&model.Role{}); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 10.检测角色 id 是否存在
func (rs *roleService) IdIsExist(role *model.Role, id uint) bool {
	if exist := variable.GormDbPostgreSql.Find(&role, id); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 9.简单列表，选择用
func (rs *roleService) GetListSimple() (temp []*vo.ListSimple, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql.Table(model.RoleTableName)

	// 角色状态为正常
	query = query.Where("state = ?", "0")

	// 排序
	query = query.Order("sort")

	// 选择需要获取的列
	query = query.Select("role_id AS value, role_code AS code, role_name AS label")

	// 查询列表
	if resultErr := query.Scan(&temp).Error; resultErr != nil {
		return nil, resultErr
	}

	return temp, err
}

// 10.用户导出CSV
func (rs *roleService) ExportCSV(req *dto.ExportReq) (downUrl string, err error) {
	headers := []string{"id", "code", "name", "sort", "state", "createdby", "createdat", "updatedby", "updatedat", "remark"}

	// 初始化查询实例
	query := variable.GormDbPostgreSql

	csvName := "sys_role"

	// 查询条件
	if req != nil {
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}

			csvName = csvName + "_" + req.CreatedAt
		} else {
			csvName = csvName + "_all"
		}
	}

	// 新建csv文件
	createFile, createErr := os.Create(paths.CreateCsvPath(csvName))
	if createErr != nil {
		return "", createErr
	}
	defer createFile.Close()

	// 获取需要写入的数据
	var entity []*model.Role
	if resultErr := query.Find(&entity).Error; resultErr != nil {
		return "", resultErr
	}

	writer := altcsv.NewWriter(createFile)
	writer.AllQuotes = true
	defer writer.Flush()

	writer.Write(headers)

	for _, item := range entity {
		line := []string{
			gconv.String(item.RoleID),
			gconv.String(item.RoleCode),
			item.RoleName,
			gconv.String(item.Sort),
			item.State,
			item.CreatedBy,
			(item.CreatedAt).Format(variable.DateFormatFull),
			item.UpdatedBy,
			(item.UpdatedAt).Format(variable.DateFormatFull),
			item.Remark,
		}

		writer.Write(line)
	}

	return paths.DownCsvPath(csvName), nil
}
