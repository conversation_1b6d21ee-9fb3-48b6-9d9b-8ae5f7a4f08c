package service

import (
	"errors"
	"os"
	"strings"

	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/app/vo"
	"lsservice/utils/gconv"

	"github.com/gin-gonic/gin"
	"github.com/tushar2708/altcsv"
)

var Post = new(postService)

type postService struct{}

// 1.获取岗位列表
func (ps *postService) GetList(req *dto.PostPageReq) (count int64, temp []*model.Post, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql
	// 查询条件
	if req != nil {
		// 岗位编码
		if req.PostCode != "" {
			query = query.Where("post_code like ?", "%"+strings.TrimSpace(req.PostCode)+"%")
		}
		// 岗位名称
		if req.PostName != "" {
			query = query.Where("post_name like ?", "%"+strings.TrimSpace(req.PostName)+"%")
		}
		// 用户状态
		if req.State != "" {
			query = query.Where("state = ?", req.State)
		}
	}

	if query.Model(&temp).Count(&count); count == 0 {
		return 0, nil, nil
	}

	// 排序
	query = query.Order("sort")

	// 分页设置
	if req.Page != nil {
		query = query.Scopes(Paginate(&req.PageReq))
	}

	// 查询列表
	if resultErr := query.Find(&temp).Error; resultErr != nil {
		return 0, nil, resultErr
	}

	return count, temp, nil
}

// 2.获取岗位详情
func (ps *postService) GetOne(id uint) (temps *model.Post, err error) {
	var entity model.Post
	// 检查 id 是否存在
	if !ps.IdIsExist(&entity, id) {
		return nil, errors.New(consts.IDNoExist)
	}

	return &entity, nil
}

// 3.新增岗位
func (ps *postService) Insert(ctx *gin.Context, req *dto.PostInsertReq) (id *uint, err error) {
	// 检查登录账号是否存在
	if ps.CheckCode(req.PostCode) {
		return nil, errors.New("已存在")
	}

	var entity model.Post
	entity.PostCode = req.PostCode
	entity.PostName = req.PostName
	entity.Sort = req.Sort
	entity.State = req.State
	entity.Remark = req.Remark

	// 写入创建者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.CreatedBy = userName
		entity.UpdatedBy = userName
	}

	if resultErr := variable.GormDbPostgreSql.Create(&entity).Error; resultErr != nil {
		return nil, resultErr
	}
	return &entity.PostID, nil
}

// 4.更新岗位
func (ps *postService) Update(ctx *gin.Context, req *dto.PostUpdateReq, id uint) (err error) {
	var entity model.Post
	// 检查 id 是否存在
	if !ps.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 检查 post_code 是否存在
	if codeExist := variable.GormDbPostgreSql.Where("post_code = ?", req.PostCode).Not("post_id = ?", id).Find(&model.Post{}); codeExist.RowsAffected > 0 {
		return errors.New("岗位编码已存在")
	}

	entity.PostCode = req.PostCode
	entity.PostName = req.PostName
	entity.Sort = req.Sort
	entity.State = req.State
	entity.Remark = req.Remark

	// 写入更新者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.UpdatedBy = userName
	}

	if resultErr := variable.GormDbPostgreSql.Save(&entity).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 5.删除岗位
func (ps *postService) Delete(ids string) (num int64, err error) {
	idArr := strings.Split(ids, ",")
	result := variable.GormDbPostgreSql.Delete(&[]model.Post{}, idArr)

	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

// 6.岗位状态更改
func (ps *postService) State(req *dto.StateReq, id uint) (err error) {
	var entity model.Post
	// 查询 id 是否存在
	if !ps.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 更新状态
	if resultErr := variable.GormDbPostgreSql.Model(&entity).Update("state", req.State).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 7.检查用户岗位编码是否存在
func (ps *postService) CheckCode(code string) bool {
	if exist := variable.GormDbPostgreSql.Take(&model.Post{}, "post_code = ?", code); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 8.检测岗位 id 是否存在
func (ps *postService) IdIsExist(post *model.Post, id uint) bool {
	if exist := variable.GormDbPostgreSql.Take(&post, id); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 9.简单列表，选择用
func (ps *postService) GetListSimple() (temp []*vo.ListSimple, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql.Table(model.PostTableName)

	// 岗位状态为正常
	query = query.Where("state = '0'")

	// 排序
	query = query.Order("sort")

	// 选择需要获取的列
	query = query.Select("post_id AS value, post_name AS label, post_code AS code")

	// 查询列表
	if resultErr := query.Scan(&temp).Error; resultErr != nil {
		return nil, resultErr
	}

	return temp, err
}

// 10.岗位导出CSV
func (ps *postService) ExportCSV(req *dto.ExportReq) (downUrl string, err error) {
	headers := []string{"id", "code", "name", "sort", "state", "createdby", "createdat", "updatedby", "updatedat", "remark"}

	// 初始化查询实例
	query := variable.GormDbPostgreSql

	csvName := "sys_post"

	// 查询条件
	if req != nil {
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}

			csvName = csvName + "_" + req.CreatedAt
		} else {
			csvName = csvName + "_all"
		}
	}

	// 新建csv文件
	createFile, createErr := os.Create(paths.CreateCsvPath(csvName))
	if createErr != nil {
		return "", createErr
	}
	defer createFile.Close()

	// 获取需要写入的数据
	var entity []*model.Post
	if resultErr := query.Find(&entity).Error; resultErr != nil {
		return "", resultErr
	}

	writer := altcsv.NewWriter(createFile)
	writer.AllQuotes = true
	defer writer.Flush()

	writer.Write(headers)

	for _, item := range entity {
		line := []string{
			gconv.String(item.PostID),
			gconv.String(item.PostCode),
			item.PostName,
			gconv.String(item.Sort),
			item.State,
			item.CreatedBy,
			(item.CreatedAt).Format(variable.DateFormatFull),
			item.UpdatedBy,
			(item.UpdatedAt).Format(variable.DateFormatFull),
			item.Remark,
		}

		writer.Write(line)
	}

	return paths.DownCsvPath(csvName), nil
}
