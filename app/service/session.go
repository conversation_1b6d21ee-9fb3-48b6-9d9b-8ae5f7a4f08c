package service

import (
	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
)

var Session = new(sessionService)

type sessionService struct{}

// 获取 Session
func (ss *sessionService) Get(ctx *gin.Context, cookieName string) (sessionId string) {
	session := sessions.Default(ctx)
	if value := session.Get(cookieName); value == nil {
		return ""
	} else {
		return value.(string)
	}
}

// 设定 Session
func (ss *sessionService) Set(ctx *gin.Context, cookieName string, sessionId string) error {
	session := sessions.Default(ctx)
	session.Set(cookieName, sessionId)

	if error := session.Save(); error != nil {
		return error
	}
	return nil
}

// 删除 Session
func (ss *sessionService) Delete(ctx *gin.Context, cookieName string) (sessionId string, err error) {
	session := sessions.Default(ctx)
	sessionId = ss.Get(ctx, cookieName)
	if sessionId == "" {
		return "", nil
	}

	session.Delete(cookieName)

	if error := session.Save(); error != nil {
		return sessionId, error
	}
	return sessionId, nil
}

// 清空 Session
func (ss *sessionService) Clear(ctx *gin.Context) error {
	session := sessions.Default(ctx)
	session.Clear()

	if error := session.Save(); error != nil {
		return error
	}
	return nil
}
