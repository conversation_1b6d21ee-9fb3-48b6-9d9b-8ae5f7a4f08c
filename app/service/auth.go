package service

import (
	"errors"
	"time"

	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/app/vo"
	"lsservice/utils"
	"lsservice/utils/md5_encrypt"

	"github.com/gin-gonic/gin"
)

var Auth = new(authService)

type authService struct{}

// 1.用户登录
func (as *authService) Login(ctx *gin.Context, req *dto.UserLoginReq) (temp *vo.UserInfoVo, err error) {
	query := variable.GormDbPostgreSql
	item := model.User{}

	// 查询用户
	if result := query.Where("user_name = ?", req.UserName).First(&item); result.RowsAffected == 0 {
		return nil, errors.New("用户名或密码错误")
	}

	// 密码校验
	verifyPassword := md5_encrypt.Base64Md5WithSalt(req.Password, item.Salt)
	if verifyPassword != item.Password {
		return nil, errors.New("用户名或密码错误")
	}

	// 判断当前用户状态
	if item.State == "1" {
		return nil, errors.New("账号被禁用，请联系管理员")
	}

	// 更新登录时间、登录IP
	loginDate := time.Now()
	clientIp := utils.GetClientIp(ctx)
	query.Model(&item).Updates(model.User{
		LoginAt: &loginDate,
		LoginIp: &clientIp,
	})

	// 生成 Session
	sessionId, err := LogOnline.Insert(ctx, req.UserName)
	if err != nil {
		return nil, errors.New("记住登录失败")
	}

	// 设置 cookie
	if sessionErr := Session.Set(ctx, consts.CookieServiceName, sessionId); sessionErr != nil {
		return nil, sessionErr
	}

	// 查找用户角色
	var userVo vo.UserInfoVo
	userVo.User = item
	userVo.RoleID = UserRole.GetRoleByUser(item.UserID)

	return &userVo, nil
}

// 2.用户登出
func (as *authService) Logout(ctx *gin.Context) (err error) {
	// 删除 cookie
	sessionId, err := Session.Delete(ctx, consts.CookieServiceName)
	if err != nil {
		return err
	}

	// 离线用户
	if sessionId != "" {
		if err := LogOnline.OffLine(&sessionId); err != nil {
			return err
		}
	}

	return nil
}
