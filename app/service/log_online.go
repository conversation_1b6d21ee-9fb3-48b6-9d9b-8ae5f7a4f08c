package service

import (
	"errors"
	"strings"
	"time"

	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/utils"

	"github.com/gin-gonic/gin"
	gonanoid "github.com/matoous/go-nanoid/v2"
)

var LogOnline = new(logOnlineService)

type logOnlineService struct{}

// 1.获取在线用户列表
func (los *logOnlineService) GetList(req *dto.LogOnlinePageReq) (count int64, temp []model.LogOnline, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql

	// 查询条件
	if req != nil {
		// 登录名称
		if req.UserName != "" {
			query = query.Where("user_name like ?", "%"+strings.TrimSpace(req.UserName)+"%")
		}

		// 登录时间
		if req.StartAt != "" {
			atArr := strings.Split(req.StartAt, ",")
			if atArr[0] != "" {
				query = query.Where("login_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("login_at <= ?", atArr[1])
			}
		}
	}

	// 查询总条数
	if query.Model(&temp).Count(&count); count == 0 {
		return 0, nil, nil
	}

	// 排序
	query = query.Order("last_access_at DESC")

	// 分页设置
	if req.Page != nil {
		query = query.Scopes(Paginate(&req.PageReq))
	}

	// 查询列表
	if resultErr := query.Find(&temp).Error; resultErr != nil {
		return 0, nil, resultErr
	}

	return count, temp, nil
}

// 2.在线用户下线
func (los *logOnlineService) OffLine(id *string) (err error) {
	var entity model.LogOnline
	// 查询 id 是否存在
	if !los.IdIsExist(&entity, *id) {
		return errors.New(consts.IDNoExist)
	}

	if result := variable.GormDbPostgreSql.Model(&entity).Update("status", "1"); result.RowsAffected == 0 {
		if result.Error != nil {
			return result.Error
		}
		return errors.New("")
	}
	return nil
}

// 3.清空离线超时用户
func (los *logOnlineService) Empty() (err error) {
	if resultErr := variable.GormDbPostgreSql.Where("status = '1'").Delete(&model.LogOnline{}).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 4.新增在线用户
func (los *logOnlineService) Insert(ctx *gin.Context, user string) (session string, err error) {
	query := variable.GormDbPostgreSql

	// 先判断是否登录过
	loginInfo := utils.GetAuthInfo(ctx)

	var exist model.LogOnline
	if isExist := query.Where(map[string]interface{}{
		"user_name": user,
		"ip_addr":   loginInfo.IpAddr,
		"browser":   loginInfo.Browser,
		"os":        loginInfo.Os,
	}).First(&exist); isExist.RowsAffected > 0 {
		num, err := los.Delete(exist.SessionID)
		if err != nil || num == 0 {
			return "", err
		}
	}

	// 新增
	nanoId, _ := gonanoid.New()
	nowTime := time.Now()

	var entity model.LogOnline
	entity.SessionID = nanoId
	entity.UserName = user
	entity.Status = "0"
	entity.AuthInfoModel = *utils.GetAuthInfo(ctx)
	entity.StartAt = &nowTime
	entity.LastAccessAt = &nowTime

	if resultErr := query.Create(&entity).Error; resultErr != nil {
		return "", resultErr
	}

	return entity.SessionID, nil
}

// 5.检测用户 id 是否存在
func (los *logOnlineService) IdIsExist(online *model.LogOnline, id string) bool {
	if exist := variable.GormDbPostgreSql.Take(&online, "session_id = ?", id); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 6.验证 Session
func (los *logOnlineService) Verify(ctx *gin.Context, sessionId string) bool {
	query := variable.GormDbPostgreSql
	var entity model.LogOnline

	// 是否存在
	if exist := query.Take(&entity, "session_id = ? AND status = '0'", sessionId); exist.RowsAffected == 0 {
		return false
	}

	// 是否超时
	nowTimeUnix := time.Now().Unix()
	lastAccessUnix := entity.LastAccessAt.Unix()

	// fmt.Println(nowTimeUnix - lastAccessUnix + 10)
	// fmt.Println(int64(entity.ExpireAt) * 60)

	if nowTimeUnix-lastAccessUnix+10 > int64(entity.ExpireTime)*60 {
		query.Model(&entity).Update("status", "2")
		return false
	}

	return true
}

// 7.更新 Session 时间
func (los *logOnlineService) UpdateDate(ctx *gin.Context, sessionId string) error {
	query := variable.GormDbPostgreSql
	var entity model.LogOnline

	// 查询 id 是否存在
	if !los.IdIsExist(&entity, sessionId) {
		return errors.New(consts.IDNoExist)
	}

	nowTime := time.Now()

	if resultErr := query.Model(&entity).Update("last_access_at", nowTime).Error; resultErr != nil {
		return resultErr
	}

	return nil
}

// 8.删除在线用户
func (los *logOnlineService) Delete(sessionId string) (num int64, err error) {
	result := variable.GormDbPostgreSql.Where("session_id = ?", sessionId).Delete(&model.LogOnline{})

	if result.Error != nil {
		return 0, result.Error
	}

	return result.RowsAffected, nil
}

// 9.获取操作用户名
func (los *logOnlineService) GetNameBySession(ctx *gin.Context) string {
	var entity model.LogOnline

	sessionId := Session.Get(ctx, consts.CookieServiceName)

	// fmt.Print(sessionId)

	if sessionId == "" {
		return ""
	}

	if resultErr := variable.GormDbPostgreSql.Take(&entity, "session_id = ?", sessionId).Error; resultErr != nil {
		return ""
	}

	return entity.UserName
}
