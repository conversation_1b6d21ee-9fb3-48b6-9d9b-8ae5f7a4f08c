package service

import (
	"errors"
	"strings"

	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/app/vo"

	"github.com/gin-gonic/gin"
)

var Menu = new(menuService)

type menuService struct{}

// 1.获取菜单树
func (ms *menuService) GetTree(req *dto.MenuReq) (temp []*vo.MenuTreeNode, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql
	// 查询条件
	if req != nil {
		// 菜单名称
		if req.MenuName != "" {
			query = query.Where("menu_name like ?", "%"+strings.TrimSpace(req.MenuName)+"%")
		}

		// 菜单显示状态
		if req.Visible != "" {
			query = query.Where("visible = ?", req.Visible)
		}
	}

	// 排序
	query = query.Order("sort")

	list := []model.Menu{}
	// 查询列表
	if resultErr := query.Find(&list).Error; resultErr != nil {
		return nil, resultErr
	}

	var menuNode vo.MenuTreeNode
	ms.makeTree(list, &menuNode, 1)

	// 搜索后不能成树的直接返回结果
	if menuNode.Children == nil && list != nil {
		for _, c := range list {
			child := &vo.MenuTreeNode{}
			child.Menu = c
			menuNode.Children = append(menuNode.Children, child)
		}
	}

	return menuNode.Children, nil
}

// 2.获取菜单详情
func (ms *menuService) GetOne(id uint) (temp *model.Menu, err error) {
	var entity model.Menu
	// 检查 id 是否存在
	if !ms.IdIsExist(&entity, id) {
		return nil, errors.New(consts.IDNoExist)
	}

	return &entity, nil
}

// 3.新增菜单
func (ms *menuService) Insert(ctx *gin.Context, req *dto.MenuInsertReq) (id *uint, err error) {
	// 检查登录账号是否存在
	if ms.CheckCode(req.MenuCode) {
		return nil, errors.New("已存在")
	}

	var entity model.Menu
	entity.MenuPid = req.MenuPid
	entity.MenuCode = req.MenuCode
	entity.MenuName = req.MenuName
	entity.MenuType = req.MenuType
	entity.Sort = req.Sort
	entity.Icon = req.Icon
	entity.Visible = req.Visible
	entity.Path = req.Path
	entity.Target = req.Target
	entity.Perms = req.Perms
	entity.IsRefresh = req.IsRefresh

	// 写入创建者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.CreatedBy = userName
		entity.UpdatedBy = userName
	}

	if resultErr := variable.GormDbPostgreSql.Create(&entity).Error; resultErr != nil {
		return nil, resultErr
	}
	return &entity.MenuID, nil
}

// 4.更新菜单
func (ms *menuService) Update(ctx *gin.Context, req *dto.MenuUpdateReq, id uint) (err error) {
	var entity model.Menu
	// 检查 id 是否存在
	if !ms.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 检查菜单编码是否存在
	if codeExist := variable.GormDbPostgreSql.Where("menu_code = ?", req.MenuCode).Not("menu_id = ?", id).Find(&model.Menu{}); codeExist.RowsAffected > 0 {
		return errors.New("菜单编码已存在")
	}

	entity.MenuPid = req.MenuPid
	entity.MenuCode = req.MenuCode
	entity.MenuName = req.MenuName
	entity.MenuType = req.MenuType
	entity.Sort = req.Sort
	entity.Icon = req.Icon
	entity.Visible = req.Visible
	entity.Path = req.Path
	entity.Target = req.Target
	entity.Perms = req.Perms
	entity.IsRefresh = req.IsRefresh

	// 写入更新者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.UpdatedBy = userName
	}

	if resultErr := variable.GormDbPostgreSql.Save(&entity).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 5.删除菜单
func (ms *menuService) Delete(ids string) (num int64, err error) {
	idArr := strings.Split(ids, ",")
	result := variable.GormDbPostgreSql.Delete(&[]model.Menu{}, idArr)

	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

// 6.菜单显示状态更改
func (ms *menuService) Visible(req *dto.MenuVisibleReq, id uint) (err error) {
	var entity model.Menu
	// 查询 id 是否存在
	if !ms.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 更新状态
	if resultErr := variable.GormDbPostgreSql.Model(&entity).Update("visible", req.Visible).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 7.获取部门树状列表
func (ms *menuService) GetTreeSimple() (temp []*vo.TreeSimple, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql

	// 部门状态为正常
	query = query.Where("visible = '0'")

	// 排序
	query = query.Order("sort")

	list := []model.Menu{}
	// 查询列表
	if resultErr := query.Find(&list).Error; resultErr != nil {
		return nil, resultErr
	}

	// fmt.Printf("%+v/n", list)

	var menuSimple vo.TreeSimple
	ms.makeTreeSimple(list, &menuSimple)

	return menuSimple.Children, nil
}

// 8.根据权限ID获取菜单
func (ms *menuService) GetMenuByRole(id uint) (temp []*vo.MenuSideTree, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql

	// 子查询：角色有的菜单权限
	subQuery := variable.GormDbPostgreSql.Select("menu_id").Where("role_id = ?", id).Table(model.RoleMenuTableName)

	query = query.Where("menu_id IN (?)", subQuery)

	query = query.Where("visible = '0'")

	query = query.Not("menu_type = 'F'")

	query = query.Order("sort")

	list := []model.Menu{}
	if resultErr := query.Find(&list).Error; resultErr != nil {
		return nil, resultErr
	}

	var menuSide vo.MenuSideTree
	ms.makeTreeSide(list, &menuSide)

	return menuSide.Children, nil
}

// 9.检查菜单编码是否存在
func (ms *menuService) CheckCode(code string) bool {
	if exist := variable.GormDbPostgreSql.Take(&model.Menu{}, "menu_code = ?", code); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 10.检测菜单 id 是否存在
func (ms *menuService) IdIsExist(menu *model.Menu, id uint) bool {
	if exist := variable.GormDbPostgreSql.Take(&menu, id); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 11.递归生成分类列表
func (ms *menuService) makeTree(menu []model.Menu, tn *vo.MenuTreeNode, levelIndex uint) {
	for _, c := range menu {
		if c.MenuPid == tn.MenuID {
			child := &vo.MenuTreeNode{}
			child.Menu = c
			child.Level = levelIndex
			tn.Children = append(tn.Children, child)
			ms.makeTree(menu, child, levelIndex+1)
		}
	}
}

// 12.递归生成分类列表
func (ms *menuService) makeTreeSimple(menu []model.Menu, tn *vo.TreeSimple) {
	for _, c := range menu {
		if c.MenuPid == tn.Key {
			child := &vo.TreeSimple{}
			child.Key = c.MenuID
			child.Title = c.MenuName
			child.Value = c.MenuID

			tn.Children = append(tn.Children, child)
			ms.makeTreeSimple(menu, child)
		}
	}
}

// 13.递归生成分类列表
func (ms *menuService) makeTreeSide(menu []model.Menu, tn *vo.MenuSideTree) {
	for _, c := range menu {
		if c.MenuPid == tn.Id {
			child := &vo.MenuSideTree{}
			child.Id = c.MenuID
			child.Key = c.MenuCode
			child.Icon = c.Icon
			child.Name = c.MenuName
			child.Path = c.Path

			tn.Children = append(tn.Children, child)
			ms.makeTreeSide(menu, child)
		}
	}
}
