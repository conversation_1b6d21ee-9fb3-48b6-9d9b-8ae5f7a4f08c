package service

import (
	"errors"
	"strings"

	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/app/vo"

	"github.com/gin-gonic/gin"
	gonanoid "github.com/matoous/go-nanoid/v2"
)

var Notice = new(noticeService)

type noticeService struct{}

// 1.获取通知公告列表
func (ns *noticeService) GetList(req *dto.NoticePageReq) (count int64, temp []vo.NoticeVo, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql

	// 查询条件
	if req != nil {
		// 公告标题
		if req.Title != "" {
			query = query.Where("title like ?", "%"+strings.TrimSpace(req.Title)+"%")
		}
		// 公告类型
		if req.Type != "" {
			query = query.Where("type = ?", req.Type)
		}
		// 公告状态
		if req.State != "" {
			query = query.Where("state = ?", req.State)
		}
		// 创建者
		if req.CreatedBy != "" {
			query = query.Where("created_by = ?", req.CreatedBy)
		}
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}
		}
	}

	// 至少一个查询条件才能做Scan查询
	query = query.Where("")

	list := model.Notice{}
	if query.Model(&list).Count(&count); count == 0 {
		return 0, nil, nil
	}

	// 排序
	if req.SortBy != "" {
		query = query.Order(req.SortBy + " " + req.Order)
	} else {
		// 默认更新时间倒序
		query = query.Order("is_top, updated_at DESC")
	}

	// 分页设置
	if req.Page != nil {
		query = query.Scopes(Paginate(&req.PageReq))
	}

	// 查询列表
	if resultErr := query.Scan(&temp).Error; resultErr != nil {
		return 0, nil, resultErr
	}

	return count, temp, nil
}

// 2.获取通知公告详情
func (ns *noticeService) GetOne(id string) (temp *model.Notice, err error) {
	var entity model.Notice
	// 检查 id 是否存在
	if !ns.IdIsExist(&entity, id) {
		return nil, errors.New(consts.IDNoExist)
	}

	return &entity, nil
}

// 3.新增通知公告
func (ns *noticeService) Insert(ctx *gin.Context, req *dto.NoticeInsertReq) (id *string, err error) {
	nanoId, _ := gonanoid.New()

	var entity model.Notice
	entity.NoticeID = nanoId
	entity.NoticeTitle = req.Title
	entity.NoticeType = req.Type
	entity.State = req.State
	entity.IsTop = req.IsTop
	entity.Remark = req.Remark

	// 写入创建者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.CreatedBy = userName
		entity.UpdatedBy = userName
	}

	// 创建文件，添加内容
	if req.Content != "" {
		path, err := Files.Insert(req.Content, "notices", entity.NoticeID, "html")
		if err != nil {
			return nil, err
		}

		entity.Path = path
	}

	if resultErr := variable.GormDbPostgreSql.Create(&entity).Error; resultErr != nil {
		return nil, resultErr
	}
	return &entity.NoticeID, nil
}

// 4.更新通知公告
func (ns *noticeService) Update(ctx *gin.Context, req *dto.NoticeUpdateReq, id string) (err error) {
	var entity model.Notice
	// 检查 id 是否存在
	if !ns.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	entity.NoticeTitle = req.Title
	entity.NoticeType = req.Type
	entity.State = req.State
	entity.IsTop = req.IsTop
	entity.Remark = req.Remark

	// 写入更新者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.UpdatedBy = userName
	}

	// 更新文件内容
	if req.Content != "" {
		if entity.Path != "" {
			if err := Files.Update(req.Content, entity.Path, "html"); err != nil {
				return err
			}
		} else {
			path, err := Files.Insert(req.Content, "notices", entity.NoticeID, "html")
			if err != nil {
				return err
			}

			entity.Path = path
		}
	}

	if resultErr := variable.GormDbPostgreSql.Save(&entity).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 5.删除通知公告
func (ns *noticeService) Delete(id string) (num int64, err error) {
	var entity model.Notice
	// 检查 id 是否存在
	if !ns.IdIsExist(&entity, id) {
		return 0, errors.New(consts.IDNoExist)
	}

	// 删除文件夹
	if err = Files.Delete(entity.Path); err != nil {
		return 0, err
	}

	result := variable.GormDbPostgreSql.Where("notice_id = ?", id).Delete(&entity)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

// 6.通知公告状态更改
func (ns *noticeService) State(req *dto.StateReq, id string) (err error) {
	var entity model.Notice
	// 检查 id 是否存在
	if !ns.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 更新状态
	if resultErr := variable.GormDbPostgreSql.Model(&entity).Update("state", req.State).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 7.检测用户 id 是否存在
func (ns *noticeService) IdIsExist(notice *model.Notice, id string) bool {
	if exist := variable.GormDbPostgreSql.Take(&notice, "notice_id = ?", id); exist.RowsAffected == 0 {
		return false
	}
	return true
}
