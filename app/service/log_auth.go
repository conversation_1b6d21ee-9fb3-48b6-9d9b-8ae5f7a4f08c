package service

import (
	"errors"
	"os"
	"strings"
	"time"

	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/utils/gconv"

	"github.com/tushar2708/altcsv"
)

var LogAuth = new(logAuthService)

type logAuthService struct{}

// 1.获取访问日志列表
func (las *logAuthService) GetList(req *dto.LogAuthPageReq) (count int64, temp []model.LogAuth, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql

	// 查询条件
	if req != nil {
		// 登录名称
		if req.UserName != "" {
			query = query.Where("user_name = ?", req.UserName)
		}
		// 访问IP地址
		if req.IpAddr != "" {
			query = query.Where("ip_addr = ?", req.IpAddr)
		}
		// 访问状态
		if req.Status != "" {
			query = query.Where("status = ?", req.Status)
		}
		// 访问时间
		if req.AuthAt != "" {
			atArr := strings.Split(req.AuthAt, ",")
			if atArr[0] != "" {
				query = query.Where("auth_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("auth_at <= ?", atArr[1])
			}
		}
	}

	if query.Model(&temp).Count(&count); count == 0 {
		return 0, nil, nil
	}

	// 排序
	query = query.Order("auth_at DESC")

	// 分页设置
	if req.Page != nil {
		query = query.Scopes(Paginate(&req.PageReq))
	}

	// 查询列表
	if resultErr := query.Find(&temp).Error; resultErr != nil {
		return 0, nil, resultErr
	}

	return count, temp, nil
}

// 2.获取访问日志详情
func (las *logAuthService) GetOne(id uint) (temp *model.LogAuth, err error) {
	var login model.LogAuth

	// 检查 id 是否存在
	if !las.IdIsExist(&login, id) {
		return nil, errors.New(consts.IDNoExist)
	}

	return &login, nil
}

// 3.新增访问日志
func (las *logAuthService) Insert(req *dto.LogAuthInsertReq) (err error) {
	var entity model.LogAuth
	entity.UserName = req.UserName
	entity.AuthInfoModel = req.AuthInfoModel
	entity.Type = req.Type
	entity.Msg = req.Msg

	// 操作状态
	if req.Status == 200 {
		entity.Status = "0"
	} else {
		entity.Status = "1"
	}

	nowTime := time.Now()
	entity.AuthAt = &nowTime

	if resultErr := variable.GormDbPostgreSql.Create(&entity).Error; resultErr != nil {
		return resultErr
	}

	return nil
}

// 4.删除访问日志
func (las *logAuthService) Delete(ids string) (err error) {
	idArr := strings.Split(ids, ",")
	if resultErr := variable.GormDbPostgreSql.Delete(&[]model.LogAuth{}, idArr).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 5.清空访问日志
func (las *logAuthService) Empty() (err error) {
	if resultErr := variable.GormDbPostgreSql.Where("1 = 1").Delete(&model.LogAuth{}).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 6.检测访问 id 是否存在
func (las *logAuthService) IdIsExist(login *model.LogAuth, id uint) bool {
	if exist := variable.GormDbPostgreSql.Take(&login, id); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 10.访问日志导出CSV
func (las *logAuthService) ExportCSV(req *dto.ExportReq) (downUrl string, err error) {
	headers := []string{"id", "username", "status", "type", "msg", "authat"}

	// 初始化查询实例
	query := variable.GormDbPostgreSql

	csvName := "sys_logauth"

	// 查询条件
	if req != nil {
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}

			csvName = csvName + "_" + req.CreatedAt
		} else {
			csvName = csvName + "_all"
		}
	}

	// 新建csv文件
	createFile, createErr := os.Create(paths.CreateCsvPath(csvName))
	if createErr != nil {
		return "", createErr
	}
	defer createFile.Close()

	// 获取需要写入的数据
	var entity []*model.LogAuth
	if resultErr := query.Find(&entity).Error; resultErr != nil {
		return "", resultErr
	}

	writer := altcsv.NewWriter(createFile)
	writer.AllQuotes = true
	defer writer.Flush()

	writer.Write(headers)

	for _, item := range entity {
		line := []string{
			gconv.String(item.AuthID),
			item.UserName,
			item.Status,
			item.Type,
			item.Msg,
			func() string {
				if item.AuthAt != nil {
					return item.AuthAt.Format(variable.DateFormatFull)
				}
				return ""
			}(),
		}

		writer.Write(line)
	}

	return paths.DownCsvPath(csvName), nil
}
