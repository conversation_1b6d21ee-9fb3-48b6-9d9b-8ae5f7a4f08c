package service

import (
	"errors"
	"os"
	"strings"

	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/app/vo"
	"lsservice/utils/gconv"

	"github.com/gin-gonic/gin"
	"github.com/tushar2708/altcsv"
)

var DictData = new(dictDataService)

type dictDataService struct{}

// 1.获取字典数据列表
func (dds *dictDataService) GetList(req *dto.DictDataPageReq) (count int64, temp []model.DictData, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql
	// 查询条件
	if req != nil {
		// 字典类型
		if req.DictType != "" {
			query = query.Where("dict_type = ?", req.DictType)
		}
		// 字典标签
		if req.DictLabel != "" {
			query = query.Where("dict_label like ?", "%"+strings.TrimSpace(req.DictLabel)+"%")
		}
		// 用户状态
		if req.State != "" {
			query = query.Where("state = ?", req.State)
		}
	}

	if query.Model(&temp).Count(&count); count == 0 {
		return 0, nil, nil
	}

	// 排序
	query = query.Order("sort")

	// 分页设置
	if req.Page != nil {
		query = query.Scopes(Paginate(&req.PageReq))
	}

	// 查询列表
	if resultErr := query.Find(&temp).Error; resultErr != nil {
		return 0, nil, resultErr
	}

	return count, temp, nil
}

// 2.获取字典数据详情
func (dds *dictDataService) GetOne(id uint) (temp *model.DictData, err error) {
	var entity model.DictData
	// 检查 code 是否存在
	if !dds.IsCodeExist(&entity, id) {
		return nil, errors.New(consts.IDNoExist)
	}

	return &entity, nil
}

// 3.新增字典数据
func (dds *dictDataService) Insert(ctx *gin.Context, req *dto.DictDataInsertReq) (idds *uint, err error) {
	// 检查登录账号是否存在
	if dds.CheckValue(req.DictType, req.DictValue) {
		return nil, errors.New("已存在相同值")
	}

	var entity model.DictData
	entity.DictLabel = req.DictLabel
	entity.DictValue = req.DictValue
	entity.DictType = req.DictType
	entity.CssClass = req.CssClass
	entity.Sort = req.Sort
	entity.ListClass = req.ListClass
	entity.IsDefault = req.IsDefault
	entity.State = req.State
	entity.Remark = req.Remark

	// 写入创建者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.CreatedBy = userName
		entity.UpdatedBy = userName
	}

	if resultErr := variable.GormDbPostgreSql.Create(&entity).Error; resultErr != nil {
		return nil, resultErr
	}
	return &entity.DictCode, nil
}

// 4.更新字典数据
func (dds *dictDataService) Update(ctx *gin.Context, req *dto.DictDataUpdateReq, id uint) (err error) {
	var entity model.DictData
	// 检查 id 是否存在
	if !dds.IsCodeExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 检查 dict_value 是否存在
	if codeExist := variable.GormDbPostgreSql.Where("dict_value= ?", req.DictValue).Not("dict_code = ?", id).First(&model.DictData{}, "dict_type= ?", req.DictType); codeExist.RowsAffected > 0 {
		return errors.New("数据值已存在")
	}

	entity.DictLabel = req.DictLabel
	entity.DictValue = req.DictValue
	entity.DictType = req.DictType
	entity.CssClass = req.CssClass
	entity.Sort = req.Sort
	entity.ListClass = req.ListClass
	entity.IsDefault = req.IsDefault
	entity.State = req.State
	entity.Remark = req.Remark

	// 写入更新者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.UpdatedBy = userName
	}

	if resultErr := variable.GormDbPostgreSql.Save(&entity).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 5.删除字典数据
func (dds *dictDataService) Delete(ids string) (num int64, err error) {
	idArr := strings.Split(ids, ",")
	result := variable.GormDbPostgreSql.Delete(&[]model.DictData{}, idArr)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

// 6.字典数据状态更改
func (dds *dictDataService) State(req *dto.StateReq, id uint) (err error) {
	var entity model.DictData
	// 查询 id 是否存在
	if !dds.IsCodeExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 更新状态
	if resultErr := variable.GormDbPostgreSql.Model(&entity).Update("state", req.State).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 7.检测是否存在相同值
func (dds *dictDataService) CheckValue(dType string, dValue string) bool {
	if exist := variable.GormDbPostgreSql.Take(&model.DictData{}, "dict_type = ? AND dict_value = ?", dType, dValue); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 8.检测数据 code 是否存在
func (dds *dictDataService) IsCodeExist(dictData *model.DictData, code uint) bool {
	if exist := variable.GormDbPostgreSql.Take(&dictData, "dict_code = ?", code); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 9.根据字典类型批量获取字典数据
func (dds *dictDataService) GetListByTypes(types string) (temp vo.DictDataMap[string, []vo.DictDataSimple], err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql

	typeArr := strings.Split(types, ",")

	// 查询条件
	query = query.Where("dict_type IN ?", typeArr)

	query = query.Where("state = '0'")

	// 排序
	query = query.Order("dict_type, sort")

	// 查询列表
	list := []model.DictData{}
	if resultErr := query.Find(&list).Error; resultErr != nil {
		return nil, resultErr
	}

	// fmt.Printf("%+v/n", list)

	// TODO 批量获取字典数据
	dataTemp := make(map[string][]vo.DictDataSimple)
	for _, dict := range list {
		dictType := dict.DictType

		if dataTemp[dictType] == nil {
			dataTemp[dictType] = []vo.DictDataSimple{}
		}

		item := vo.DictDataSimple{}

		item.Label = dict.DictLabel
		item.Value = dict.DictValue
		item.Text = *dict.CssClass
		item.Tag = *dict.ListClass
		item.Default = dict.IsDefault

		// fmt.Printf("%+v/n", item)

		dataTemp[dictType] = append(dataTemp[dictType], item)
	}
	// fmt.Printf("%+v/n", dataTemp)

	return dataTemp, nil
}

// 10.根据备注查询字典数据（用于日志）
func (dds *dictDataService) GetValueToLog(dictType string, remark string) (value string, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql

	var entity model.DictData

	// 查询条件
	query = query.Where("dict_type = ? AND remark like ?", dictType, remark)

	if resultErr := query.Find(&entity).Error; resultErr != nil {
		return "", resultErr
	}

	return entity.DictValue, nil
}

// 10.字典数据导出CSV
func (dds *dictDataService) ExportCSV(req *dto.ExportReq) (downUrl string, err error) {
	headers := []string{"code", "label", "value", "type", "sort", "default", "state", "createdby", "createdat", "updatedby", "updatedat", "remark"}

	// 初始化查询实例
	query := variable.GormDbPostgreSql

	csvName := "sys_dictdata"

	// 查询条件
	if req != nil {
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}

			csvName = csvName + "_" + req.CreatedAt
		} else {
			csvName = csvName + "_all"
		}
	}

	// 新建csv文件
	createFile, createErr := os.Create(paths.CreateCsvPath(csvName))
	if createErr != nil {
		return "", createErr
	}
	defer createFile.Close()

	// 获取需要写入的数据
	var entity []*model.DictData
	if resultErr := query.Find(&entity).Error; resultErr != nil {
		return "", resultErr
	}

	writer := altcsv.NewWriter(createFile)
	writer.AllQuotes = true
	defer writer.Flush()

	writer.Write(headers)

	for _, item := range entity {
		line := []string{
			gconv.String(item.DictCode),
			item.DictLabel,
			item.DictValue,
			item.DictType,
			gconv.String(item.Sort),
			gconv.String(item.IsDefault),
			item.State,
			item.CreatedBy,
			(item.CreatedAt).Format(variable.DateFormatFull),
			item.UpdatedBy,
			(item.UpdatedAt).Format(variable.DateFormatFull),
			item.Remark,
		}

		writer.Write(line)
	}

	return paths.DownCsvPath(csvName), nil
}
