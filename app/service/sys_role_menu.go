package service

import (
	"fmt"
	"strings"

	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/utils/gconv"
)

// 角色和菜单关系: 角色1-N菜单
var RoleMenu = new(roleMenuService)

type roleMenuService struct{}

// 1.根据角色ID获取菜单ID
func (rms *roleMenuService) GetMenuByRole(id uint) *string {
	// 初始化查询实例
	query := variable.GormDbPostgreSql.Where("role_id = ?", id)

	list := []model.RoleMenu{}
	if result := query.Find(&list); result.Error != nil {
		return nil
	}

	temp := make([]string, len(list))
	for i, v := range list {
		temp[i] = fmt.Sprintf("%d", v.MenuID)
	}

	menuIds := strings.Join(temp, ",")

	return &menuIds
}

// 2. 新增角色与菜单
func (rms *roleMenuService) Insert(roleId uint, menuIds string) error {
	menuIdsArr := strings.Split(menuIds, ",")

	if len(menuIdsArr) > 0 {
		roleMenu := []model.RoleMenu{}

		for _, v := range menuIdsArr {
			item := model.RoleMenu{}

			item.RoleID = roleId
			item.MenuID = gconv.Uint(v)

			roleMenu = append(roleMenu, item)
		}

		// fmt.Printf("%+v\n", roleMenu)

		result := variable.GormDbPostgreSql.Create(&roleMenu)

		// fmt.Printf("%+v\n", result)

		if result.Error != nil {
			return result.Error
		}

		return nil
	}

	return nil
}

// 3.删除角色与菜单
func (rms *roleMenuService) Delete(roleId uint) error {
	result := variable.GormDbPostgreSql.Where("role_id = ?", roleId).Delete(&model.RoleMenu{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}
