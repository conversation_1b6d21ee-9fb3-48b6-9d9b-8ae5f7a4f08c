package service

import (
	"fmt"
	"strings"

	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/utils/gconv"
)

// 用户与岗位关系: 用户1-N岗位
var UserPost = new(userPostService)

type userPostService struct{}

// 1. 根据用户ID获取岗位ID
func (ups *userPostService) GetPostByUser(id uint) *string {
	// 初始化查询实例
	query := variable.GormDbPostgreSql.Where("user_id = ?", id)

	list := []model.UserPost{}
	if result := query.Find(&list); result.RowsAffected == 0 {
		return nil
	}

	// fmt.Printf("%+v\n", list)

	temp := make([]string, len(list))
	for i, v := range list {
		temp[i] = fmt.Sprintf("%d", v.PostID)
	}

	postIds := strings.Join(temp, ",")

	return &postIds
}

// 2. 新增用户与岗位
func (ups *userPostService) Insert(userId uint, postIds string) error {
	postIdsArr := strings.Split(postIds, ",")

	if len(postIdsArr) > 0 {
		userPost := []model.UserPost{}

		for _, v := range postIdsArr {
			item := model.UserPost{}

			item.UserID = userId
			item.PostID = gconv.Uint(v)

			userPost = append(userPost, item)
		}

		// fmt.Printf("%+v\n", userPost)

		result := variable.GormDbPostgreSql.Create(&userPost)

		// fmt.Printf("%+v\n", result)

		if result.Error != nil {
			return result.Error
		}

		return nil
	}

	return nil
}

// 3.删除用户与岗位
func (ups *userPostService) Delete(userId uint) error {
	result := variable.GormDbPostgreSql.Where("user_id = ?", userId).Delete(&model.UserPost{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// 4.删除用户与岗位
func (ups *userPostService) DeleteAll(userIds []string) error {
	result := variable.GormDbPostgreSql.Where("user_id IN ?", userIds).Delete(&model.UserPost{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}
