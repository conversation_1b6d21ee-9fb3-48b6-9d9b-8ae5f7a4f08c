package service

import (
	"time"

	"lsservice/app/vo"

	"github.com/shirou/gopsutil/v4/cpu"
	"github.com/shirou/gopsutil/v4/disk"
	"github.com/shirou/gopsutil/v4/host"
	"github.com/shirou/gopsutil/v4/mem"
)

var Server = new(serverService)

type serverService struct{}

// 1.获取服务器信息
func (s *serverService) GetInfo() (temp *vo.ServerInfoVo, err error) {
	var info vo.ServerInfoVo

	// 获取本机信息
	h, hErr := host.Info()
	if hErr == nil {
		// fmt.Println("本机信息：", h)
		info.Sys = &vo.SysInfo{
			Hostname:        h.Hostname,
			Uptime:          h.Uptime,
			BootTime:        h.BootTime,
			Procs:           h.Procs,
			OS:              h.OS,
			Platform:        h.Platform,
			PlatformFamily:  h.PlatformFamily,
			PlatformVersion: h.PlatformVersion,
			KernelVersion:   h.KernelVersion,
			KernelArch:      h.KernelArch,
		}
	}

	// 获取CPU信息
	c, cErr := cpu.Info()
	if cErr == nil {
		// fmt.Println("cpu信息:", c)
		info.Cpu = &vo.CpuInfo{
			ModelName: c[0].ModelName,
			Cores:     c[0].Cores,
			Mhz:       c[0].Mhz,
		}

		c1, _ := cpu.Percent(time.Duration(time.Second), false)
		// fmt.Println("cpu使用率:", c1)
		info.Cpu.Used = c1
	}

	// 获取物理内存信息
	m, mErr := mem.VirtualMemory()
	if mErr == nil {
		// fmt.Println("物理内存信息:", m)
		info.Mem = &vo.UsedInfo{
			Total:       m.Total,
			Free:        m.Free,
			Used:        m.Used,
			UsedPercent: m.UsedPercent,
		}
	}

	// 获取虚拟内存信息
	m1, m1Err := mem.SwapMemory()
	if m1Err == nil {
		// fmt.Println("虚拟内存信息:", m1)
		info.Swap = &vo.UsedInfo{
			Total:       m1.Total,
			Free:        m1.Free,
			Used:        m1.Used,
			UsedPercent: m1.UsedPercent,
		}
	}

	// 获取硬盘信息
	d, dErr := disk.Usage("/")
	if dErr == nil {
		// fmt.Println("硬盘信息:", d)
		info.Disk = &vo.UsedInfo{
			Total:       d.Total,
			Free:        d.Free,
			Used:        d.Used,
			UsedPercent: d.UsedPercent,
		}
	}

	return &info, nil
}
