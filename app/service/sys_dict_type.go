package service

import (
	"errors"
	"os"
	"strings"

	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/utils/gconv"

	"github.com/gin-gonic/gin"
	"github.com/tushar2708/altcsv"
)

var DictType = new(dictTypeService)

type dictTypeService struct{}

// 1.获取字典类型列表
func (dts *dictTypeService) GetList(req *dto.DictTypePageReq) (count int64, temp []model.DictType, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql
	// 查询条件
	if req != nil {
		// 字典名称
		if req.DictName != "" {
			query = query.Where("dict_name like ?", "%"+strings.TrimSpace(req.DictName)+"%")
		}
		// 字典类型
		if req.DictType != "" {
			query = query.Where("dict_type like ?", "%"+strings.TrimSpace(req.DictType)+"%")
		}
		// 用户状态
		if req.State != "" {
			query = query.Where("state = ?", req.State)
		}
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}
		}
	}

	if query.Model(&temp).Count(&count); count == 0 {
		return 0, nil, nil
	}

	// 排序
	if req.SortBy != "" {
		query = query.Order(req.SortBy + " " + req.Order)
	} else {
		// 默认更新时间倒序
		query = query.Order("created_at DESC")
	}
	// 分页设置
	if req.Page != nil {
		query = query.Scopes(Paginate(&req.PageReq))
	}

	// 查询列表
	if resultErr := query.Find(&temp).Error; resultErr != nil {
		return 0, nil, resultErr
	}

	return count, temp, nil
}

// 2.获取字典类型详情
func (dts *dictTypeService) GetOne(id uint) (temp *model.DictType, err error) {
	var entity model.DictType
	// 检查 id 是否存在
	if !dts.IdIsExist(&entity, id) {
		return nil, errors.New(consts.IDNoExist)
	}

	return &entity, nil
}

// 3.新增字典类型
func (dts *dictTypeService) Insert(ctx *gin.Context, req *dto.DictTypeInsertReq) (idts *uint, err error) {
	// 检查登录账号是否存在
	if dts.CheckType(req.DictType) {
		return nil, errors.New("已存在")
	}

	var entity model.DictType
	entity.DictName = req.DictName
	entity.DictType = req.DictType
	entity.State = req.State
	entity.Remark = req.Remark

	// 写入创建者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.CreatedBy = userName
		entity.UpdatedBy = userName
	}

	if resultErr := variable.GormDbPostgreSql.Create(&entity).Error; resultErr != nil {
		return nil, resultErr
	}
	return &entity.DictID, nil
}

// 4.更新字典类型
func (dts *dictTypeService) Update(ctx *gin.Context, req *dto.DictTypeUpdateReq, id uint) (err error) {
	var entity model.DictType
	// 检查 id 是否存在
	if !dts.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 检查 dictType_type 是否存在
	if codeExist := variable.GormDbPostgreSql.Where("dictType_type= ?", req.DictType).Not("dictType_id = ?", id).First(&model.DictType{}); codeExist.RowsAffected > 0 {
		return errors.New("数据类型已存在")
	}

	entity.DictName = req.DictName
	entity.DictType = req.DictType
	entity.State = req.State
	entity.Remark = req.Remark

	// 写入更新者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.UpdatedBy = userName
	}

	if resultErr := variable.GormDbPostgreSql.Save(&entity).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 5.删除字典类型
func (dts *dictTypeService) Delete(ids string) (num int64, err error) {
	idArr := strings.Split(ids, ",")
	result := variable.GormDbPostgreSql.Delete(&[]model.DictType{}, idArr)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

// 6.字典类型状态更改
func (dts *dictTypeService) State(req *dto.StateReq, id uint) (err error) {
	var entity model.DictType
	// 查询 id 是否存在
	if !dts.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 更新状态
	if resultErr := variable.GormDbPostgreSql.Model(&entity).Update("state", req.State).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 7.检查数据类别是否存在
func (dts *dictTypeService) CheckType(dType string) bool {
	if exist := variable.GormDbPostgreSql.Take(&model.DictType{}, "dictType_type = ?", dType); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 8.检测数据 id 是否存在
func (dts *dictTypeService) IdIsExist(dictType *model.DictType, id uint) bool {
	if exist := variable.GormDbPostgreSql.Take(&dictType, id); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 10.字典类型导出CSV
func (dts *dictTypeService) ExportCSV(req *dto.ExportReq) (downUrl string, err error) {
	headers := []string{"id", "type", "name", "state", "createdby", "createdat", "updatedby", "updatedat", "remark"}

	// 初始化查询实例
	query := variable.GormDbPostgreSql

	csvName := "sys_dicttype"

	// 查询条件
	if req != nil {
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}

			csvName = csvName + "_" + req.CreatedAt
		} else {
			csvName = csvName + "_all"
		}
	}

	// 新建csv文件
	createFile, createErr := os.Create(paths.CreateCsvPath(csvName))
	if createErr != nil {
		return "", createErr
	}
	defer createFile.Close()

	// 获取需要写入的数据
	var entity []*model.DictType
	if resultErr := query.Find(&entity).Error; resultErr != nil {
		return "", resultErr
	}

	writer := altcsv.NewWriter(createFile)
	writer.AllQuotes = true
	defer writer.Flush()

	writer.Write(headers)

	for _, item := range entity {
		line := []string{
			gconv.String(item.DictID),
			item.DictType,
			item.DictName,
			item.State,
			item.CreatedBy,
			(item.CreatedAt).Format(variable.DateFormatFull),
			item.UpdatedBy,
			(item.UpdatedAt).Format(variable.DateFormatFull),
			item.Remark,
		}

		writer.Write(line)
	}

	return paths.DownCsvPath(csvName), nil
}
