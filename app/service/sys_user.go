package service

import (
	"errors"
	"os"
	"strings"
	"time"

	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/app/vo"
	"lsservice/utils/gconv"
	"lsservice/utils/md5_encrypt"

	"github.com/gin-gonic/gin"
	"github.com/tushar2708/altcsv"
)

var User = new(userService)

type userService struct{}

// 1.获取用户列表
func (us *userService) GetList(req *dto.UserPageReq) (count int64, temp []*model.User, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql

	// 查询条件
	if req != nil {
		// 登录名称
		if req.UserName != "" {
			query = query.Where("user_name like ?", "%"+strings.TrimSpace(req.UserName)+"%")
		}
		// 手机号码
		if req.Mobile != "" {
			query = query.Where("mobile like ?", "%"+strings.TrimSpace(req.Mobile)+"%")
		}
		// 用户状态
		if req.State != "" {
			query = query.Where("state = ?", req.State)
		}
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}
		}

		// 部门
		if req.DeptID != 0 {
			query = query.Where("dept_id = ?", req.DeptID)
		}
	}

	// 查询总条数
	if query.Model(&temp).Count(&count); count == 0 {
		return 0, nil, nil
	}

	// 排序
	if req.SortBy != "" {
		query = query.Order(req.SortBy + " " + req.Order)
	} else {
		// 默认更新时间倒序
		query = query.Order("created_at DESC")
	}

	// 分页设置
	// offset := (req.Page - 1) * req.Limit
	// query = query.Limit(req.Limit).Offset(offset)
	if req.Page != nil {
		query = query.Scopes(Paginate(&req.PageReq))
	}

	// 查询列表
	if resultErr := query.Find(&temp).Error; resultErr != nil {
		return 0, nil, resultErr
	}

	return count, temp, nil
}

// 2.获取用户详情
// func (us *userService) GetOne(id uint) (temp *vo.UserInfoVo, err error) {
// 	var user model.User

// 	// 检查 id 是否存在
// 	if !us.IdIsExist(&user, id) {
// 		return nil, errors.New(consts.IDNoExist)
// 	}

// 	result := &vo.UserInfoVo{}
// 	result.User = user

// 	// 岗位
// 	result.PostIDs = UserPost.GetPostByUser(user.UserID)
// 	// 角色
// 	result.RoleID = UserRole.GetRoleByUser(user.UserID)

//		return result, nil
//	}
func (us *userService) GetOne(id uint) (temp *vo.UserInfoVo, err error) {
	// 检查 id 是否存在
	if !us.IdIsExist(&model.User{}, id) {
		return nil, errors.New(consts.IDNoExist)
	}

	// 初始化查询实例
	query := variable.GormDbPostgreSql.Table(model.UserTableName + " AS u")

	query = query.Select("u.*, ur.role_id, STRING_AGG(distinct up.post_id::varchar, ',') as post_ids")

	// Left Join
	query = query.Joins("left join " + model.UserRoleTableName + " AS ur on u.user_id=ur.user_id")
	query = query.Joins("left join " + model.UserPostTableName + " AS up on u.user_id=up.user_id")

	query = query.Where("u.user_id = ?", id)

	query = query.Group("u.user_id, ur.role_id")

	if result := query.Find(&temp); result.RowsAffected == 0 {
		if result.Error != nil {
			return nil, result.Error
		} else {
			return nil, errors.New(consts.NoData)
		}
	}

	return temp, nil
}

// 3.新增用户
func (us *userService) Insert(ctx *gin.Context, req *dto.UserInsertReq) (id *uint, err error) {
	query := variable.GormDbPostgreSql

	// 检查登录账号是否存在
	if us.CheckUser(req.UserName) {
		return nil, errors.New(consts.NameIsExist)
	}

	var entity model.User
	entity.DeptID = req.DeptID
	entity.UserName = req.UserName
	entity.NickName = req.NickName
	entity.RealName = req.RealName
	entity.Mobile = req.Mobile
	entity.Email = req.Email
	entity.Gender = req.Gender
	entity.State = req.State
	entity.Remark = req.Remark

	// entity.Salt = variable.SnowFlakeNode.Generate().String()
	salt := gconv.String(time.Now().Unix())
	entity.Salt = salt
	entity.Password = md5_encrypt.Base64Md5WithSalt(req.Password, salt)

	// 写入创建者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.CreatedBy = userName
		entity.UpdatedBy = userName
	}

	if resultErr := query.Create(&entity).Error; resultErr != nil {
		return nil, resultErr
	}

	// 插入 岗位、角色
	if req.PostIds != nil {
		UserPost.Insert(entity.UserID, *req.PostIds)
	}
	if req.RoleId != nil {
		UserRole.Insert(entity.UserID, *req.RoleId)
	}

	return &entity.UserID, nil
}

// 4.更新用户
func (us *userService) Update(ctx *gin.Context, req *dto.UserUpdateReq, id uint) (err error) {
	query := variable.GormDbPostgreSql

	var entity model.User
	// 检查 id 是否存在
	if !us.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 检查 user_name 是否存在
	if nameExist := query.Where("user_name = ?", req.UserName).Not("user_id = ?", id).Find(&model.User{}); nameExist.RowsAffected > 0 {
		return errors.New("登录账号已存在")
	}

	entity.DeptID = req.DeptID
	entity.UserName = req.UserName
	entity.NickName = req.NickName
	entity.RealName = req.RealName
	entity.Mobile = req.Mobile
	entity.Email = req.Email
	entity.Gender = req.Gender
	entity.State = req.State
	entity.Remark = req.Remark

	// 写入更新者
	userName := LogOnline.GetNameBySession(ctx)
	if userName != "" {
		entity.UpdatedBy = userName
	}

	if resultErr := query.Save(&entity).Error; resultErr != nil {
		return resultErr
	}

	// 更新 岗位、角色
	if req.PostIds != nil {
		UserPost.Delete(entity.UserID)
		UserPost.Insert(entity.UserID, *req.PostIds)
	}
	if req.RoleId != nil {
		UserRole.Delete(entity.UserID)
		UserRole.Insert(entity.UserID, *req.RoleId)
	}

	return nil
}

// 5.删除用户
func (us *userService) Delete(ids string) (num int64, err error) {
	idArr := strings.Split(ids, ",")

	result := variable.GormDbPostgreSql.Delete(&[]model.User{}, idArr)

	if result.Error != nil {
		return 0, result.Error
	}

	// 删除 岗位、角色
	UserPost.DeleteAll(idArr)
	UserRole.DeleteAll(idArr)

	return result.RowsAffected, nil
}

// 6.用户状态更改
func (us *userService) State(req *dto.StateReq, id uint) (err error) {
	var entity model.User
	// 查询 id 是否存在
	if !us.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 更新状态
	if result := variable.GormDbPostgreSql.Model(&entity).Update("state", req.State); result.RowsAffected == 0 {
		if result.Error != nil {
			return result.Error
		}
		return errors.New("")
	}
	return nil
}

// 7.用户密码重置
func (us *userService) ResetPwd(req *dto.UserResetPwdReq, id uint) (err error) {
	var entity model.User
	// 检查 id 是否存在
	if !us.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 判断新密码两次是否一致
	if req.Password != req.RePassword {
		return errors.New("两次密码不一致")
	}

	updPwdDate := time.Now()
	// salt := variable.SnowFlakeNode.Generate().String()
	salt := gconv.String(time.Now().Unix())
	password := md5_encrypt.Base64Md5WithSalt(req.Password, salt)

	if result := variable.GormDbPostgreSql.Model(&entity).Updates(&model.User{
		Password:     password,
		Salt:         salt,
		PwdUpdatedAt: &updPwdDate,
	}); result.Error != nil {
		return result.Error
	}
	return nil
}

// 8.用户密码更改
func (us *userService) UpdatePwd(req *dto.UserUpdatePwdReq, id uint) (err error) {
	var entity model.User
	// 检查 id 是否存在
	if !us.IdIsExist(&entity, id) {
		return errors.New(consts.IDNoExist)
	}

	// 判断旧密码是否正确
	if entity.Password != md5_encrypt.Base64Md5WithSalt(req.OldPassword, entity.Salt) {
		return errors.New("旧密码不正确")
	}

	// 判断新密码两次是否一致
	if req.NewPassword != req.RePassword {
		return errors.New("两次密码不一致")
	}

	updPwdDate := time.Now()
	salt := gconv.String(time.Now().Unix())
	password := md5_encrypt.Base64Md5WithSalt(req.NewPassword, salt)

	if result := variable.GormDbPostgreSql.Model(&entity).Updates(&model.User{
		Password:     password,
		Salt:         salt,
		PwdUpdatedAt: &updPwdDate,
	}); result.Error != nil {
		return result.Error
	}
	return nil
}

// 9.用户导入CSV
func (us *userService) ImportCSV(ctx *gin.Context, req *dto.ImportReq) (err error) {
	return nil
}

// 10.用户导出CSV
func (us *userService) ExportCSV(req *dto.ExportReq) (downUrl string, err error) {
	headers := []string{"id", "deptid", "username", "nickname", "realname", "type", "mobile", "email", "gender", "state", "loginip", "loginat", "pwdupdatedat", "createdby", "createdat", "updatedby", "updatedat", "remark"}

	// 初始化查询实例
	query := variable.GormDbPostgreSql

	csvName := "sys_user"

	// 查询条件
	if req != nil {
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}

			csvName = csvName + "_" + req.CreatedAt
		} else {
			csvName = csvName + "_all"
		}
	}

	// 新建csv文件
	createFile, createErr := os.Create(paths.CreateCsvPath(csvName))
	if createErr != nil {
		return "", createErr
	}
	defer createFile.Close()

	// 获取需要写入的数据
	var entity []*model.User
	if resultErr := query.Find(&entity).Error; resultErr != nil {
		return "", resultErr
	}

	writer := altcsv.NewWriter(createFile)
	writer.AllQuotes = true
	defer writer.Flush()

	writer.Write(headers)

	for _, item := range entity {
		line := []string{
			gconv.String(item.UserID),
			gconv.String(item.DeptID),
			item.UserName,
			item.NickName,
			item.RealName,
			item.UserType,
			item.Mobile,
			item.Email,
			item.Gender,
			item.State,
			func() string {
				if item.LoginIp != nil {
					return *item.LoginIp
				}
				return ""
			}(),
			func() string {
				if item.LoginAt != nil {
					return item.LoginAt.Format(variable.DateFormatFull)
				}
				return ""
			}(),
			func() string {
				if item.PwdUpdatedAt != nil {
					return item.PwdUpdatedAt.Format(variable.DateFormatFull)
				}
				return ""
			}(),
			item.CreatedBy,
			(item.CreatedAt).Format(variable.DateFormatFull),
			item.UpdatedBy,
			(item.UpdatedAt).Format(variable.DateFormatFull),
			item.Remark,
		}

		writer.Write(line)
	}

	return paths.DownCsvPath(csvName), nil
}

// 11.检查用户登录账号是否存在
func (us *userService) CheckUser(name string) bool {
	if exist := variable.GormDbPostgreSql.Take(&model.User{}, "user_name = ?", name); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 12.检测用户 id 是否存在
func (us *userService) IdIsExist(user *model.User, id uint) bool {
	if exist := variable.GormDbPostgreSql.Take(&user, id); exist.RowsAffected == 0 {
		return false
	}
	return true
}
