package service

import (
	"fmt"
	"strings"

	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/utils/gconv"
)

// 角色和部门关系: 角色1-N部门
var RoleDept = new(roleDeptService)

type roleDeptService struct{}

// 1.根据角色ID获取部门ID
func (rds *roleDeptService) GetDeptByRole(id uint) *string {
	query := variable.GormDbPostgreSql.Where("role_id = ?", id)

	list := []model.RoleDept{}
	if result := query.Find(&list); result.Error != nil {
		return nil
	}

	temp := make([]string, len(list))
	for i, v := range list {
		temp[i] = fmt.Sprintf("%d", v.DeptID)
	}

	deptIds := strings.Join(temp, ",")

	return &deptIds
}

// 2. 新增角色与部门
func (rds *roleDeptService) Insert(roleId uint, deptIds string) error {
	deptIdsArr := strings.Split(deptIds, ",")

	if len(deptIdsArr) > 0 {
		roleDept := []model.RoleDept{}

		for _, v := range deptIdsArr {
			item := model.RoleDept{}

			item.RoleID = roleId
			item.DeptID = gconv.Uint(v)

			roleDept = append(roleDept, item)
		}

		// fmt.Printf("%+v\n", roleDept)

		result := variable.GormDbPostgreSql.Create(&roleDept)

		// fmt.Printf("%+v\n", result)

		if result.Error != nil {
			return result.Error
		}

		return nil
	}

	return nil
}

// 3.删除角色与部门
func (rds *roleDeptService) Delete(roleId uint) error {
	result := variable.GormDbPostgreSql.Where("role_id = ?", roleId).Delete(&model.RoleDept{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}
