package service

import (
	"encoding/json"
	"errors"
	"os"
	"strings"
	"time"

	"lsservice/app/dto"
	"lsservice/app/global/consts"
	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
	"lsservice/app/model"
	"lsservice/utils/gconv"

	"github.com/tushar2708/altcsv"
)

var LogOper = new(logOperService)

type logOperService struct{}

// 1.获取操作日志列表
func (los *logOperService) GetList(req *dto.LogOperPageReq) (count int64, temp []*model.LogOper, err error) {
	// 初始化查询实例
	query := variable.GormDbPostgreSql

	// 查询条件
	if req != nil {
		// 系统模块
		if req.Module != "" {
			query = query.Where("module = ?", req.Module)
		}
		// 操作人员
		if req.OperName != "" {
			query = query.Where("oper_name = ?", req.OperName)
		}
		// 操作类型
		if req.BusinessType != "" {
			query = query.Where("business_type = ?", req.BusinessType)
		}
		// 操作时间
		if req.OperAt != "" {
			atArr := strings.Split(req.OperAt, ",")
			if atArr[0] != "" {
				query = query.Where("oper_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("oper_at <= ?", atArr[1])
			}
		}
	}

	if query.Model(&temp).Count(&count); count == 0 {
		return 0, nil, nil
	}

	// 排序
	query = query.Order("oper_at DESC")

	// 分页设置
	if req.Page != nil {
		query = query.Scopes(Paginate(&req.PageReq))
	}

	// 查询列表
	if resultErr := query.Find(&temp).Error; resultErr != nil {
		return 0, nil, resultErr
	}

	return count, temp, nil
}

// 2.获取操作日志详情
func (los *logOperService) GetOne(id uint) (temp *model.LogOper, err error) {
	var oper model.LogOper

	// 检查 id 是否存在
	if !los.IdIsExist(&oper, id) {
		return nil, errors.New(consts.IDNoExist)
	}

	return &oper, nil
}

// 3.新增操作日志
func (los *logOperService) Insert(req *dto.LogOperInsertReq) (err error) {
	var entity model.LogOper
	entity.Module = req.Module
	entity.BusinessType = req.BusinessType
	entity.Method = req.Method
	entity.RequestMethod = req.RequestMethod
	entity.OperName = req.OperName
	entity.Url = req.Url
	entity.IpAddr = req.IpAddr
	entity.Location = req.Location
	entity.StatusCode = req.StatusCode

	// Encode param as JSON
	paramJSON, err := json.Marshal(req.Param)
	if err == nil {
		entity.Param = string(paramJSON)
	}

	// Ensure JsonResult is valid JSON
	if req.JsonResult != "" {
		var js json.RawMessage
		if err := json.Unmarshal([]byte(req.JsonResult), &js); err == nil {
			entity.JsonResult = req.JsonResult
		}
	}

	nowTime := time.Now()
	entity.OperAt = &nowTime

	if resultErr := variable.GormDbPostgreSql.Create(&entity).Error; resultErr != nil {
		return resultErr
	}

	return nil
}

// 4.删除操作日志
func (los *logOperService) Delete(ids string) (err error) {
	idArr := strings.Split(ids, ",")
	if resultErr := variable.GormDbPostgreSql.Delete(&[]model.LogOper{}, idArr).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 5.清空操作日志
func (los *logOperService) Empty() (err error) {
	if resultErr := variable.GormDbPostgreSql.Where("1 = 1").Delete(&model.LogOper{}).Error; resultErr != nil {
		return resultErr
	}
	return nil
}

// 6.检测操作 id 是否存在
func (los *logOperService) IdIsExist(oper *model.LogOper, id uint) bool {
	if exist := variable.GormDbPostgreSql.Take(&oper, id); exist.RowsAffected == 0 {
		return false
	}
	return true
}

// 10.操作日志导出CSV
func (las *logOperService) ExportCSV(req *dto.ExportReq) (downUrl string, err error) {
	headers := []string{"id", "module", "type", "method", "request", "opername", "url", "ip", "location", "param", "status", "result", "operat"}

	// 初始化查询实例
	query := variable.GormDbPostgreSql

	csvName := "sys_logoper"

	// 查询条件
	if req != nil {
		// 创建时间
		if req.CreatedAt != "" {
			atArr := strings.Split(req.CreatedAt, ",")
			if atArr[0] != "" {
				query = query.Where("created_at >= ?", atArr[0])
			}
			if atArr[1] != "" {
				query = query.Where("created_at <= ?", atArr[1])
			}

			csvName = csvName + "_" + req.CreatedAt
		} else {
			csvName = csvName + "_all"
		}
	}

	// 新建csv文件
	createFile, createErr := os.Create(paths.CreateCsvPath(csvName))
	if createErr != nil {
		return "", createErr
	}
	defer createFile.Close()

	// 获取需要写入的数据
	var entity []*model.LogOper
	if resultErr := query.Find(&entity).Error; resultErr != nil {
		return "", resultErr
	}

	writer := altcsv.NewWriter(createFile)
	writer.AllQuotes = true
	defer writer.Flush()

	writer.Write(headers)

	for _, item := range entity {
		line := []string{
			gconv.String(item.OperID),
			item.Module,
			item.BusinessType,
			item.Method,
			item.RequestMethod,
			item.OperName,
			item.Url,
			item.IpAddr,
			item.Location,
			item.Param,
			gconv.String(item.StatusCode),
			item.JsonResult,
			func() string {
				if item.OperAt != nil {
					return item.OperAt.Format(variable.DateFormatFull)
				}
				return ""
			}(),
		}

		writer.Write(line)
	}

	return paths.DownCsvPath(csvName), nil
}
