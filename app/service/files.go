package service

import (
	"os"
	"time"

	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
)

var Files = new(filesService)

type filesService struct{}

func (fs *filesService) Insert(fileContent string, fileType string, fileId string, fileSuffix string) (path string, err error) {
	content := []byte(fileContent)

	// 创建文件夹路径
	foldDate := time.Now().Format("2006/01")
	basePath := "/" + fileType + "/" + foldDate + "/" + fileId
	// fmt.Println(savePath)

	savePath := variable.BasePath + paths.SymApp + basePath
	if err := os.MkdirAll(savePath, os.ModePerm); err != nil {
		return "", err
	}

	// 保存 html 路径
	file := savePath + "/index." + fileSuffix

	if err := os.WriteFile(file, content, 0o644); err != nil {
		return "", err
	}

	return basePath, nil
}

func (fs *filesService) Update(fileContent string, path string, fileSuffix string) (err error) {
	content := []byte(fileContent)

	updatePath := variable.BasePath + paths.SymApp + path

	// 判断目录是否存在，不存在创建
	if _, err := os.Stat(updatePath); err != nil {
		if cerr := os.MkdirAll(updatePath, os.ModePerm); cerr != nil {
			return cerr
		}
	}

	file := updatePath + "/index." + fileSuffix
	if err := os.WriteFile(file, content, 0o644); err != nil {
		return err
	}

	return nil
}

func (fs *filesService) Delete(path string) (err error) {
	deletePath := variable.BasePath + paths.SymApp + path
	if os.RemoveAll(deletePath) != nil {
		return err
	}

	return nil
}
