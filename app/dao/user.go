package dao

import (
	"lsservice/app/global/variable"
	"lsservice/app/model"
)

type UserDao interface {
	Add(user *model.User) error
	Update(user *model.User) error
	Delete(user *model.User) error
	FindById(id uint) (*model.User, error)
}

type UserDaoImpl struct{}

// 添加
func (u *UserDaoImpl) Add(user *model.User) error {
	if resultErr := variable.GormDbPostgreSql.Create(user).Error; resultErr != nil {
		return resultErr
	}
	return nil
}
