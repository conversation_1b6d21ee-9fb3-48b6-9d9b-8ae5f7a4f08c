package model

import (
	"gorm.io/plugin/soft_delete"
)

const DeptTableName = "s_manage.tb_sys_dept"

type Dept struct {
	DeptID    uint                  `gorm:"primaryKey;autoIncrement" json:"deptId"`
	DeptPid   uint                  `gorm:"default:0" json:"deptPid"`
	Ancestors string                `gorm:"default:''" json:"ancestors"`
	DeptCode  string                `gorm:"not null" json:"code"`
	DeptName  string                `gorm:"default:''" json:"name"`
	Sort      uint                  `gorm:"default:0" json:"sort"`
	Leader    *string               `json:"leader"`
	State     string                `gorm:"default:'0'" json:"state"`
	DelFlag   soft_delete.DeletedAt `gorm:"softDelete:flag" json:"-"`
	IsSystem  string                `gorm:"default:'0'" json:"isSystem"`
	BaseModel                       // 生成、更新信息
}

// 表名
func (d *Dept) TableName() string {
	return DeptTableName
}
