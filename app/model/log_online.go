package model

import "time"

const LogOnlineTableName = "s_manage.tb_log_online"

type LogOnline struct {
	SessionID string `gorm:"primaryKey;autoIncrement" json:"sessionId"`
	UserName  string `gorm:"default:''" json:"userName"`
	Status    string `gorm:"default:''" json:"status"`
	AuthInfoModel
	StartAt      *time.Time `json:"startAt"`
	LastAccessAt *time.Time `json:"lastAccessAt"`
	ExpireTime   int        `gorm:"default:30" json:"expireTime"`
}

// 表名
func (lo *LogOnline) TableName() string {
	return LogOnlineTableName
}
