package model

import (
	"time"
)

const LogAuthTableName = "s_manage.tb_log_auth"

type LogAuth struct {
	AuthID   uint   `gorm:"primaryKey;autoIncrement" json:"authId"`
	UserName string `gorm:"default:''" json:"userName"`
	AuthInfoModel
	Status string     `gorm:"default:''" json:"status"`
	Type   string     `gorm:"default:''" json:"type"`
	Msg    string     `gorm:"default:''" json:"msg"`
	AuthAt *time.Time `json:"authAt"`
}

// 表名
func (la *LogAuth) TableName() string {
	return LogAuthTableName
}
