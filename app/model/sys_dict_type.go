package model

const DictTypeTableName = "s_manage.tb_dict_type"

type DictType struct {
	DictID    uint   `gorm:"primaryKey;autoIncrement" json:"dictId"`
	DictName  string `gorm:"default:''" json:"name"`
	DictType  string `gorm:"not null" json:"type"`
	State     string `gorm:"default:'0'" json:"state"`
	IsSystem  string `gorm:"default:'0'" json:"isSystem"`
	BaseModel        // 生成、更新信息
	Remark    string `json:"remark"`
}

// 表名
func (dt *DictType) TableName() string {
	return DictTypeTableName
}
