package model

const DictDataTableName = "s_manage.tb_dict_data"

type DictData struct {
	DictCode  uint    `gorm:"primaryKey;autoIncrement" json:"code"`
	DictLabel string  `gorm:"default:''" json:"label"`
	DictValue string  `gorm:"not null" json:"value"`
	DictType  string  `gorm:"default:''" json:"type"`
	Sort      uint    `gorm:"default:0" json:"sort"`
	CssClass  *string `json:"cssClass"`
	ListClass *string `json:"listClass"`
	IsDefault string  `gorm:"default:'0'" json:"isDefault"`
	State     string  `gorm:"default:'0'" json:"state"`
	IsSystem  string  `gorm:"default:'0'" json:"isSystem"`
	BaseModel         // 生成、更新信息
	Remark    string  `json:"remark"`
}

// 表名
func (dd *DictData) TableName() string {
	return DictDataTableName
}
