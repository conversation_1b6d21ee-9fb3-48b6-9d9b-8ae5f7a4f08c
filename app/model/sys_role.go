package model

import (
	"gorm.io/plugin/soft_delete"
)

const RoleTableName = "s_manage.tb_sys_role"

type Role struct {
	RoleID    uint                  `gorm:"primaryKey;autoIncrement" json:"roleId"`
	RoleCode  string                `gorm:"not null" json:"code"`
	RoleName  string                `gorm:"default:''" json:"name"`
	Sort      uint                  `json:"sort"`
	DataScope string                `gorm:"default:'0'" json:"dataScope"`
	State     string                `gorm:"default:'0'" json:"state"`
	DelFlag   soft_delete.DeletedAt `gorm:"softDelete:flag" json:"-"`
	IsSystem  string                `gorm:"default:'0'" json:"isSystem"`
	BaseModel                       // 生成、更新信息
	Remark    string                `json:"remark"`
}

// 表名
func (u *Role) TableName() string {
	return RoleTableName
}
