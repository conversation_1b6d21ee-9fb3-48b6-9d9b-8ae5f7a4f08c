package model

import (
	"time"
)

type BaseModel struct {
	CreatedBy string    `json:"createdBy"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedBy string    `json:"updatedBy"`
	UpdatedAt time.Time `json:"updatedAt"`
}

type AuthInfoModel struct {
	IpAddr         string `json:"ipAddr"`
	Location       string `json:"location"`
	Browser        string `json:"browser"`
	BrowserVersion string `json:"browserVer"`
	Os             string `json:"os"`
	OsVersion      string `json:"osVer"`
	Device         string `json:"device"`
}
