package model

const ConfigTableName = "s_manage.tb_other_config"

type Config struct {
	ConfigID    uint   `gorm:"primaryKey;autoIncrement" json:"configId"`
	ConfigKey   string `gorm:"not null" json:"key"`
	ConfigName  string `gorm:"default:''" json:"name"`
	ConfigValue string `gorm:"not null" json:"value"`
	IsSystem    string `gorm:"default:'0'" json:"isSystem"`
	BaseModel          // 生成、更新信息
	Remark      string `json:"remark"`
}

// 表名
func (c *Config) TableName() string {
	return ConfigTableName
}
