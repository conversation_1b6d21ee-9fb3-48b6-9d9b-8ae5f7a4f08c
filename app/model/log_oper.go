package model

import (
	"time"
)

const LogOperTableName = "s_manage.tb_log_oper"

type LogOper struct {
	OperID        uint       `gorm:"primaryKey;autoIncrement" json:"operId"`
	Module        string     `gorm:"default:''" json:"module"`
	BusinessType  string     `gorm:"default:'00'" json:"type"`
	Method        string     `gorm:"default:''" json:"method"`
	RequestMethod string     `gorm:"default:''" json:"requestMethod"`
	OperatorType  string     `gorm:"default:'0'" json:"operatorType"`
	OperName      string     `gorm:"default:''" json:"operName"`
	Url           string     `gorm:"default:''" json:"url"`
	IpAddr        string     `gorm:"default:''" json:"ipAddr"`
	Location      string     `gorm:"default:''" json:"location"`
	Param         string     `gorm:"default:''" json:"param"`
	StatusCode    int        `gorm:"default:200" json:"status"`
	JsonResult    string     `gorm:"default:''" json:"jsonResult"`
	ErrorMsg      string     `gorm:"default:''" json:"errorMsg"`
	OperAt        *time.Time `json:"operAt"`
	CostTime      uint       `gorm:"default:0" json:"costTime"`
}

// 表名
func (lo *LogOper) TableName() string {
	return LogOperTableName
}
