package model

const PostTableName = "s_manage.tb_sys_post"

type Post struct {
	PostID    uint   `gorm:"primaryKey;autoIncrement" json:"postId"`
	PostCode  string `gorm:"not null" json:"code"`
	PostName  string `gorm:"default:''" json:"name"`
	Sort      uint   `gorm:"default:0" json:"sort"`
	State     string `gorm:"default:'0'" json:"state"`
	IsSystem  string `gorm:"default:'0'" json:"isSystem"`
	BaseModel        // 生成、更新信息
	Remark    string `json:"remark"`
}

// 表名
func (p *Post) TableName() string {
	return PostTableName
}
