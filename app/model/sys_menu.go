package model

const MenuTableName = "s_manage.tb_sys_menu"

type Menu struct {
	MenuID    uint    `gorm:"primaryKey;autoIncrement" json:"menuId"`
	MenuPid   uint    `gorm:"default:0" json:"menuPid"`
	MenuType  string  `gorm:"default:''" json:"type"`
	MenuCode  string  `gorm:"not null" json:"code"`
	MenuName  string  `gorm:"default:''" json:"name"`
	Sort      uint    `gorm:"default:0" json:"sort"`
	Path      string  `gorm:"default:'#'" json:"path"`
	Target    string  `gorm:"default:'0'" json:"target"`
	Visible   string  `gorm:"default:'0'" json:"visible"`
	IsRefresh string  `gorm:"default:'0'" json:"isRefresh"`
	Perms     *string `json:"perms"`
	Icon      string  `gorm:"default:''" json:"icon"`
	IsSystem  string  `gorm:"default:'0'" json:"isSystem"`
	BaseModel         // 生成、更新信息
	Remark    string  `json:"remark"`
}

// 表名
func (m *Menu) TableName() string {
	return MenuTableName
}
