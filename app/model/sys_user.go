package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const UserTableName = "s_manage.tb_sys_user"

type User struct {
	UserID       uint                  `gorm:"primaryKey;autoIncrement" json:"userId"`
	DeptID       *uint                 `json:"deptId"`
	UserName     string                `gorm:"not null" json:"userName"`
	NickName     string                `gorm:"default:''" json:"nickName"`
	RealName     string                `gorm:"default:''" json:"realName"`
	UserType     string                `gorm:"default:'00'" json:"userType"`
	Mobile       string                `gorm:"default:''" json:"mobile"`
	Email        string                `gorm:"default:''" json:"email"`
	Gender       string                `gorm:"default:'0'" json:"gender"`
	Avatar       string                `gorm:"default:''" json:"avatar"`
	Password     string                `json:"-"`
	Salt         string                `json:"-"`
	State        string                `gorm:"default:'0'" json:"state"`
	DelFlag      soft_delete.DeletedAt `gorm:"softDelete:flag" json:"-"`
	LoginCount   uint                  `json:"-"`
	LoginIp      *string               `json:"loginIp"`
	LoginAt      *time.Time            `json:"loginAt"`
	PwdUpdatedAt *time.Time            `json:"pwdUpdatedAt"`
	IsSystem     string                `gorm:"default:'0'" json:"isSystem"`
	BaseModel                          // 生成、更新信息
	Remark       string                `json:"remark"`
}

// 表名
func (u *User) TableName() string {
	return UserTableName
}

// // 根据条件查询单条数据
// func (u *UserModel) Get() (bool, error) {
// 	return utils.XormDb.Get(u)
// }

// // 插入数据
// func (u *UserModel) Insert() (int64, error) {
// 	return utils.XormDb.Insert(u)
// }

// // 更新数据
// func (u *UserModel) Update() (int64, error) {
// 	return utils.XormDb.Id(u.ID).Update(u)
// }

// // 删除
// func (u *UserModel) Delete() (int64, error) {
// 	return utils.XormDb.Id(u.ID).Delete(&UserModel{})
// }

// //批量删除
// func (u *UserModel) BatchDelete(ids ...int64) (int64, error) {
// 	return utils.XormDb.In("id", ids).Delete(&UserModel{})
// }
