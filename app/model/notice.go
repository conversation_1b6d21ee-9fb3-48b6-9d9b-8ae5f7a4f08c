package model

const NoticeTableName = "s_manage.tb_other_notice"

type Notice struct {
	NoticeID    string `gorm:"primaryKey" json:"noticeId"`
	NoticeTitle string `gorm:"not null" json:"title"`
	NoticeType  string `gorm:"not null" json:"type"`
	State       string `gorm:"default:'0'" json:"state"`
	Path        string `json:"path"`
	IsTop       string `gorm:"default:'0'" json:"isTop"`
	Browse      uint   `gorm:"default:0" json:"browse"`
	BaseModel          // 生成、更新信息
	Remark      string `json:"remark"`
}

// 表名
func (n *Notice) TableName() string {
	return NoticeTableName
}
