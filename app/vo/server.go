package vo

type SysInfo struct {
	Hostname        string `json:"hostname"`
	Uptime          uint64 `json:"uptime"`
	BootTime        uint64 `json:"bootTime"`
	Procs           uint64 `json:"procs"`
	OS              string `json:"os"`
	Platform        string `json:"platform"`
	PlatformFamily  string `json:"platformFamily"`
	PlatformVersion string `json:"platformVersion"`
	KernelVersion   string `json:"kernelVersion"`
	KernelArch      string `json:"kernelArch"`
}

type CpuInfo struct {
	ModelName string    `json:"modelName"`
	Cores     int32     `json:"cores"`
	Mhz       float64   `json:"mhz"`
	Used      []float64 `json:"used"`
}

type UsedInfo struct {
	Total       uint64  `json:"total"`
	Free        uint64  `json:"free"`
	Used        uint64  `json:"used"`
	UsedPercent float64 `json:"usedPercent"`
}

type ServerInfoVo struct {
	Sys  *SysInfo  `json:"sys"`
	Cpu  *CpuInfo  `json:"cpu"`
	Mem  *UsedInfo `json:"mem"`
	Swap *UsedInfo `json:"swap"`
	Disk *UsedInfo `json:"disk"`
}
