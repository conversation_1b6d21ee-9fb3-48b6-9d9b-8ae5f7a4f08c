package vo

// type DictDataSimple struct {
// 	DictType  string `json:"type"`
// 	DictLabel string `json:"label"`
// 	DictValue string `json:"value"`
// 	CssClass  string `json:"cssClass"`
// 	ListClass string `json:"listClass"`
// }

type DictDataSimple struct {
	Label   string `json:"label"`
	Value   string `json:"value"`
	Text    string `json:"text"`
	Tag     string `json:"tag"`
	Default string `json:"default"`
}

type DictDataMap[KEY string, VALUE []DictDataSimple] map[KEY]VALUE
