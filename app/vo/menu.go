package vo

import "lsservice/app/model"

// 菜单数表格Vo
type MenuTreeNode struct {
	model.Menu
	Level    uint            `json:"level"`
	Children []*MenuTreeNode `json:"children"` // 子菜单
}

// 菜单栏
type MenuSide struct {
	Id   uint   `json:"-"`
	Key  string `json:"key"`
	Icon string `json:"icon"`
	Name string `json:"name"`
	Path string `json:"path"`
}

// 菜单栏树
type MenuSideTree struct {
	MenuSide
	Children []*MenuSideTree `json:"children"`
}

// type MenuSimple struct {
// 	MenuID   uint   `json:"menuId"`
// 	MenuPid  uint   `json:"menuPid"`
// 	MenuCode string `json:"code"`
// 	MenuName string `json:"name"`
// }

// // 部门树列表Vo
// type MenuTreeSimple struct {
// 	MenuSimple
// 	Children []*MenuTreeSimple `json:"children"`
// }
