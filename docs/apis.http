# @BASE_URL = http://127.0.0.1:8081/
@BASE_URL = http://************:8081/
# @contentType = application/x-www-form-urlencoded;charset=utf-8
# @jsessionid = JSESSIONID=da87e5a9-35a9-4bed-98be-62426ee1d6cc

# application/x-www-form-urlencoded
# multipart/form-data
# application/json
# text/xml

@contentTypeJson = application/json
@contentTypeForm = multipart/form-data

@LOGIN = auth/login
@LOGOUT = auth/logout
@CHECKLOGIN = logincheck

@USER = manage/users
@ROLE = manage/roles
@MENU = manage/menus
@SHOP = manage/shops
@POST = manage/posts
@DICT_TYPE = manage/dicttypes
@DICT_DATA = manage/dictdatas
@CONFIG = manage/configs
@NOTICE = manage/notices
@LOG_OPER = manage/logopers
@LOG_AUTH = manage/logauths
@LOG_ONLINE = manage/logonlines
@UPLOAD_IMG = manage/upload/img
@UPLOAD_FILE = manage/upload/file

### --- --- --- 用户相关 START --- --- ---

### 1.登录
POST {{BASE_URL}}{{LOGIN}} HTTP/1.1
Content-Type: {{contentTypeJson}}
X-Forwarded-For: *************
User-Agent: Mozilla/5.0 (iPhone; U; CPU iPhone OS 5_1_1 like Mac OS X; en) AppleWebKit/534.46.0 (KHTML, like Gecko) CriOS/19.0.1084.60 Mobile/9B206 Safari/7534.48.3

  {
    "userName": "admin",
    "password": "123456",
    "captcha": "111111",
    "rememberMe": true
  }

### 2.登出
POST {{BASE_URL}}{{LOGOUT}} HTTP/1.1
Content-Type: {{contentTypeJson}}

### 2-1.检查是否已登录
GET {{BASE_URL}}{{CHECKLOGIN}} HTTP/1.1

### 3.获取用户列表
GET {{BASE_URL}}{{USER}}
  ?page=1
  &limit=10
  &sortby=created_at
  &order=desc
  &userName=
  &mobile=
  &status=
  &createdAt=
  &deptId=

### 4.获取单个用户
GET {{BASE_URL}}{{USER}}/1 HTTP/1.1

### 5.新增用户
POST {{BASE_URL}}{{USER}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "userName": "user3",
    "nickName": "测试3",
    "shopId": 100,
    "mobile": "13000000001",
    "email": "<EMAIL>",
    "password": "123456",
    "gender": "1",
    "postIds": "1,2",
    "roleId": 1,
    "remark": "xxxxx"
  }

### 6.更新用户
PUT {{BASE_URL}}{{USER}}/3 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "userName": "aaaa2",
    "nickName": "赵大大2",
    "shopId": 103,
    "mobile": "13000000003",
    "email": "<EMAIL>",
    "gender": "1",
    "status": "1",
    "postIds": "3,4",
    "roleId": 2,
    "remark": "xx3333xx"
  }

### 7.删除用户
DELETE {{BASE_URL}}{{USER}}/3 HTTP/1.1

### 8.用户状态更改
PATCH {{BASE_URL}}{{USER}}/status/4 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "status": "0"
  }

### 9.用户密码重置
PATCH {{BASE_URL}}{{USER}}/resetpwd/4 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "password": "654321",
    "rePassword": "654321"
  }


### 10.用户密码更改
PATCH {{BASE_URL}}{{USER}}/updatepwd/6 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "oldPassword": "123456",
    "newPassword": "654321",
    "rePassword": "654321"
  }

### 9.用户名称查询
GET {{BASE_URL}}{{USER}}/checkuser
  ?username=admin

### --- --- --- 用户相关 END --- --- ---

### --- --- --- 角色相关 START --- --- ---

### 1.获取角色列表
GET {{BASE_URL}}{{ROLE}}
  ?page=1
  &limit=10
  &sortby=
  &order=
  &name=
  &key=
  &status=
  &createdAt=

### 2.获取单个角色
GET {{BASE_URL}}{{ROLE}}/1 HTTP/1.1

### 3.新增角色
POST {{BASE_URL}}{{ROLE}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "code": "administrator",
    "name": "超级管理员",
    "sort": 1,
    "status": "0",
    "remark": "超级管理员",
    "menuIds": null
  }

### 4.更新角色
PUT {{BASE_URL}}{{ROLE}}/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "code": "admin",
    "name": "管理员",
    "sort": 1,
    "status": "0",
    "remark": "1232",
    "menuIds": "1,2"
  }

### 5.删除角色
DELETE {{BASE_URL}}{{ROLE}}/1 HTTP/1.1


### 6.角色状态更改
PATCH {{BASE_URL}}{{ROLE}}/status/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "status": "0"
  }

### 8.数据权限
PUT {{BASE_URL}}{{ROLE}}/data/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "code": "admin",
    "name": "管理员",
    "dataScope": "1",
    "deptIds": "1,2"
  }

### 9.获取角色简单列表
GET {{BASE_URL}}{{ROLE}}/simple HTTP/1.1

### --- --- --- 角色相关 END --- --- ---

### --- --- --- 菜单相关 START --- --- ---

### 1.获取菜单树
GET {{BASE_URL}}{{MENU}}
    ?name=
    &visible=

### 2.获取单个菜单
GET {{BASE_URL}}{{MENU}}/1 HTTP/1.1

### 3.新增菜单
POST {{BASE_URL}}{{MENU}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "pid": 0,
    "type": "M",
    "code": "sys1",
    "name": "系统管理",
    "sort": 2,
    "visible": "1"
  }

### 4.更新菜单
PUT {{BASE_URL}}{{MENU}}/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "pid": 0,
    "type": "M",
    "code": "sys",
    "name": "系统管理1233356",
    "sort": 1,
    "visible": "0",
    "icon": ""
  }

### 5.删除菜单
DELETE {{BASE_URL}}{{MENU}}/2 HTTP/1.1

### 6.菜单显示更改
PATCH {{BASE_URL}}{{MENU}}/visible/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "visible": "1"
  }

### 7.获取菜单树
GET {{BASE_URL}}{{MENU}}/tree

### 8.根据权限获取菜单
GET {{BASE_URL}}{{MENU}}/role/1

### --- --- --- 菜单相关 END --- --- ---

### --- --- --- 店铺相关 START --- --- ---

### 1.获取店铺列表
GET {{BASE_URL}}{{SHOP}}
  ?page=1
  &limit=10
  &sortby=
  &order=
  &name=
  &status=

### 2.获取单个店铺
GET {{BASE_URL}}{{SHOP}}/1 HTTP/1.1

### 3.新增店铺
POST {{BASE_URL}}{{SHOP}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "name": "麻吉(总店)",
    "status": "0",
    "logo": "",
    "address": "xxx路"
  }

### 4.更新店铺
PUT {{BASE_URL}}{{SHOP}}/8 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "pid": 100,
    "code": "it",
    "name": "技术部2",
    "sort": 2,
    "leader": "XXX",
    "status": "1"
  }

### 5.删除店铺
DELETE {{BASE_URL}}{{SHOP}}/7 HTTP/1.1

### 6.店铺状态更改
PATCH {{BASE_URL}}{{SHOP}}/status/9 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "status": "1"
  }

### 7.获取店铺简单列表
GET {{BASE_URL}}{{SHOP}}/simple

### --- --- --- 店铺相关 END --- --- ---

### --- --- --- 岗位相关 START --- --- ---

### 1.获取岗位列表
GET {{BASE_URL}}{{POST}}
    ?page=1
    &limit=10
    &code=
    &name=
    &status=

### 2.获取单个岗位
GET {{BASE_URL}}{{POST}}/1 HTTP/1.1

### 3.新增岗位
POST {{BASE_URL}}{{POST}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "code": "ceoo",
    "name": "董事长",
    "sort": 1,
    "status": "0",
    "remark": "123"
  }

### 4.更新岗位
PUT {{BASE_URL}}{{POST}}/5 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "code": "ceoo",
    "name": "董事长234",
    "sort": 1,
    "status": "0",
    "remark": "2345"
  }

### 5.删除岗位
DELETE {{BASE_URL}}{{POST}}/2 HTTP/1.1

### 6.岗位状态更改
PATCH {{BASE_URL}}{{POST}}/status/5 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "status": "1"
  }

### 7.获取岗位简单列表
GET {{BASE_URL}}{{POST}}/simple HTTP/1.1

### --- --- --- 岗位相关 END --- --- ---

### --- --- --- 字典类型相关 START --- --- ---

### 1.获取字典类型列表
GET {{BASE_URL}}{{DICT_TYPE}}
    ?page=1
    &limit=10
    &name=
    &type=
    &status=

### 2.获取单个字典类型
GET {{BASE_URL}}{{DICT_TYPE}}/1 HTTP/1.1

### 3.新增字典类型
POST {{BASE_URL}}{{DICT_TYPE}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "name": "用户性别",
    "type": "sys_user_sex",
    "status": "0",
    "remark": "用户性别列表"
  }

### 4.更新字典类型
PUT {{BASE_URL}}{{DICT_TYPE}}/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "name": "用户性别",
    "type": "sys_user_sex",
    "status": "0",
    "remark": "用户性别列表123"
  }

### 5.删除字典类型
DELETE {{BASE_URL}}{{DICT_TYPE}}/1 HTTP/1.1

### 6.字典类型状态更改
PATCH {{BASE_URL}}{{DICT_TYPE}}/status/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "status": "1"
  }

### --- --- --- 字典类型相关 END --- --- ---

### --- --- --- 字典数据相关 START --- --- ---

### 1.获取字典数据列表
GET {{BASE_URL}}{{DICT_DATA}}
    ?page=0
    &limit=10
    &type=sys_user_sex
    &label=
    &status=

### 2.获取单个字典数据
GET {{BASE_URL}}{{DICT_DATA}}/1 HTTP/1.1

### 3.新增字典数据
POST {{BASE_URL}}{{DICT_DATA}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "label": "男",
    "value": "0",
    "type": "sys_user_sex",
    "cssClass": "",
    "sort": 1,
    "listClass": "",
    "isDefault": "0",
    "status": "0",
    "remark": "性别男"
  }

### 4.更新字典数据
PUT {{BASE_URL}}{{DICT_DATA}}/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "label": "女",
    "value": "1",
    "type": "sys_user_sex",
    "cssClass": "",
    "sort": 1,
    "listClass": "",
    "isDefault": "0",
    "status": "0",
    "remark": "性别女"
  }

### 5.删除字典数据
DELETE {{BASE_URL}}{{DICT_DATA}}/1 HTTP/1.1

### 6.字典数据状态更改
PATCH {{BASE_URL}}{{DICT_DATA}}/status/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "status": "1"
  }

### 7.根据字典类型获取数据列表
GET {{BASE_URL}}{{DICT_DATA}}/lists
  ?types=sys_user_sex,sys_normal_disabled

### --- --- --- 字典数据相关 END --- --- ---

### --- --- --- 参数配置相关 START --- --- ---

### 1.获取参数配置列表
GET {{BASE_URL}}{{CONFIG}}
    ?page=1
    &limit=10
    &name=
    &key=
    &type=

### 2.获取单个参数配置
GET {{BASE_URL}}{{CONFIG}}/1 HTTP/1.1

### 3.新增参数配置
POST {{BASE_URL}}{{CONFIG}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "name": "默认密码",
    "key": "sys_user_password",
    "value": "123456",
    "type": "Y",
    "remark": "默认配置密码"
  }

### 4.更新参数配置
PUT {{BASE_URL}}{{CONFIG}}/1 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "name": "默认密码",
    "key": "sys_user_password",
    "value": "654321",
    "type": "Y",
    "remark": "默认配置密码"
  }

### 5.删除参数配置
DELETE {{BASE_URL}}{{CONFIG}}/1 HTTP/1.1

### --- --- --- 参数配置相关 END --- --- ---

### --- --- --- 通知公告相关 START --- --- ---

### 1.获取通知公告列表
GET {{BASE_URL}}{{NOTICE}}
    ?page=1
    &limit=10
    &title=
    &createdBy=
    &createdAt=

### 2.获取单个通知公告
GET {{BASE_URL}}{{NOTICE}}/68092668-1324-4981-9587-7a944ee86c8b HTTP/1.1

### 3.新增通知公告
POST {{BASE_URL}}{{NOTICE}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "title": "一个通知",
    "type": "1",
    "content": "<p>这是一个通知，大家来看一看</p><p>123</p>",
    "status": "0",
    "remark": "测试用的"
  }

### 4.更新通知公告
PUT {{BASE_URL}}{{NOTICE}}/68092668-1324-4981-9587-7a944ee86c8b HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "title": "一个通知-改",
    "type": "1",
    "content": "<p>这是一个通知，大家来看一看，错了</p><p>12345</p>",
    "status": "0",
    "remark": "测试用的1"
  }

### 5.删除通知公告
DELETE {{BASE_URL}}{{NOTICE}}/60c2249a-7ced-4d9c-9e8d-76fb1126f0f9 HTTP/1.1

### 6.通知公告状态更改
PATCH {{BASE_URL}}{{NOTICE}}/status/26d4acd6-8681-44c1-91c5-54581cd31157 HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "status": "1"
  }

### --- --- --- 通知公告相关 END --- --- ---

### --- --- --- 操作日志相关 START --- --- ---

### 1.获取操作日志列表
GET {{BASE_URL}}{{LOG_OPER}}
    ?page=0
    &limit=10
    &title=
    &operName=
    &type=
    &status=
    &operAt=

### 2.获取单个操作日志
GET {{BASE_URL}}{{LOG_OPER}}/1 HTTP/1.1

### 3.删除操作日志
DELETE {{BASE_URL}}{{LOG_OPER}}/1 HTTP/1.1

### 4.清空操作日志
DELETE {{BASE_URL}}{{LOG_OPER}} HTTP/1.1

### --- --- --- 操作日志相关 END --- --- ---

### --- --- --- 登录日志相关 START --- --- ---

### 1.获取登录日志列表
GET {{BASE_URL}}{{LOG_AUTH}}
    ?page=0
    &limit=10
    &title=
    &operName=
    &type=
    &status=
    &operAt=

### 2.获取单个登录日志
GET {{BASE_URL}}{{LOG_AUTH}}/1 HTTP/1.1

### 3.删除登录日志
DELETE {{BASE_URL}}{{LOG_AUTH}}/1 HTTP/1.1

### 4.清空登录日志
DELETE {{BASE_URL}}{{LOG_AUTH}} HTTP/1.1

### --- --- --- 登录日志相关 END --- --- ---

### --- --- --- 在线用户相关 START --- --- ---

### 1.获取在线用户列表
GET {{BASE_URL}}{{LOG_ONLINE}}
    ?page=0
    &limit=10
    &username=
    &ipaddr=

### 2.在线用户强退
PUT {{BASE_URL}}{{LOG_ONLINE}}/offline/1 HTTP/1.1

### 4.清空离线用户
DELETE {{BASE_URL}}{{LOG_ONLINE}} HTTP/1.1

### --- --- --- 在线用户相关 END --- --- ---


### --- --- --- 文件上传 START --- --- ---

### 图片上传
POST {{BASE_URL}}{{UPLOAD_IMG}} HTTP/1.1
Content-Type: {{contentTypeForm}}; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="type"

image
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="logo4.jpg"
Content-Type: image/jpeg

< /Downloads/logo4.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 文件上传
POST {{BASE_URL}}{{UPLOAD_FILE}} HTTP/1.1
Content-Type: {{contentTypeForm}}; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="type"

image
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="logo4.jpg"
Content-Type: image/jpeg

< /Downloads/logo4.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW--


### --- --- --- 文件上传 END --- --- ---
