@BASE_URL = http://127.0.0.1:8092/web
# @BASE_URL = http://************:8082/web

@contentTypeJson = application/json
@contentTypeForm = multipart/form-data

@LOGIN = /login
@REGISTER = /register
@LOGOUT = /logout
@RESETPWD = /resetpwd

@DOC = /docs
@APP = /apps

### --- --- --- 用户相关 START --- --- ---

### 1.登录
POST {{BASE_URL}}{{LOGIN}} HTTP/1.1
Content-Type: {{contentTypeJson}}
X-Forwarded-For: *************

  {
    "username": "xxx",
    "password": "123456",
    "captcha": "111111",
    "rememberMe": true
  }

### 2.注册
POST {{BASE_URL}}{{REGISTER}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "email": "<EMAIL>",
    "username": "xxx",
    "password": "123456",
    "repassword": "123456"
  }

### 3.登出
POST {{BASE_URL}}{{LOGOUT}} HTTP/1.1
Content-Type: {{contentTypeJson}}

### 4.重置密码
POST {{BASE_URL}}{{RESETPWD}} HTTP/1.1
Content-Type: {{contentTypeJson}}

  {
    "email": "<EMAIL>"
  }

### 5.网站获取文档
GET {{BASE_URL}}{{DOC}}
  ?page=1
  &limit=10
  &title=
  &type=

### 5-1.网站文档详情
GET {{BASE_URL}}{{DOC}}/xz4sk_wgpdG5xaAUy-qR9 HTTP/1.1

### 5.网站获取下载文件
GET {{BASE_URL}}{{APP}}
  ?page=1
  &limit=10
  &title=
  &type=
