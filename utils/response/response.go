package response

import (
	"net/http"
	"reflect"

	"lsservice/app/global/consts"
	"lsservice/app/global/my_errors"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	// "goskeleton/app/utils/validator_translation"
)

func ReturnJson(Context *gin.Context, httpCode int, dataCode int, msg string, data interface{}) {
	// Context.Header("key2020","value2020")  	//可以根据实际情况在头部添加额外的其他信息
	Context.JSON(httpCode, gin.H{
		"code": dataCode,
		"msg":  msg,
		"data": data,
	})
}

// ReturnJsonFromString 将json字符窜以标准json格式返回（例如，从redis读取json格式的字符串，返回给浏览器json格式）
func ReturnJsonFromString(Context *gin.Context, httpCode int, jsonStr string) {
	Context.Header("Content-Type", "application/json; charset=utf-8")
	Context.String(httpCode, jsonStr)
}

// 语法糖函数封装

// Success 直接返回成功
func Success(c *gin.Context, msg string, data interface{}) {
	ReturnJson(c, http.StatusOK, consts.CurdStatusOkCode, msg, data)
}

// Success 分页返回成功
func SuccessByPage(c *gin.Context, msg string, count int64, data interface{}) {
	c.JSON(http.StatusOK, gin.H{
		"code":  consts.CurdStatusOkCode,
		"msg":   msg,
		"count": count,
		"list":  data,
	})
}

// 图片上传
func ImageResponse(c *gin.Context, dataCode int, msg string, errno int, data interface{}) {
	// data: url, alt, href
	c.JSON(http.StatusOK, gin.H{
		"code":    dataCode,
		"errno":   errno,
		"message": msg,
		"data":    data,
	})
}

// Fail 失败的业务逻辑
func Fail(c *gin.Context, dataCode int, msg string, data interface{}) {
	ReturnJson(c, http.StatusBadRequest, dataCode, msg, data)
	c.Abort()
}

// token 基本的格式错误
func ErrorTokenBaseInfo(c *gin.Context) {
	ReturnJson(c, http.StatusBadRequest, http.StatusBadRequest, my_errors.ErrorsTokenBaseInfo, "")
	// 终止可能已经被加载的其他回调函数的执行
	c.Abort()
}

// token 权限校验失败
func ErrorTokenAuthFail(c *gin.Context) {
	ReturnJson(c, http.StatusUnauthorized, http.StatusUnauthorized, my_errors.ErrorsNoAuthorization, "")
	// 终止可能已经被加载的其他回调函数的执行
	c.Abort()
}

// token 不符合刷新条件
func ErrorTokenRefreshFail(c *gin.Context) {
	ReturnJson(c, http.StatusBadRequest, http.StatusBadRequest, my_errors.ErrorsRefreshTokenFail, "")
	// 终止可能已经被加载的其他回调函数的执行
	c.Abort()
}

// casbin 鉴权失败，返回 405 方法不允许访问
func ErrorCasbinAuthFail(c *gin.Context, msg interface{}) {
	ReturnJson(c, http.StatusMethodNotAllowed, http.StatusMethodNotAllowed, my_errors.ErrorsCasbinNoAuthorization, msg)
	c.Abort()
}

// 参数校验错误
func ErrorParam(c *gin.Context, wrongParam interface{}) {
	ReturnJson(c, http.StatusBadRequest, consts.ValidatorParamsCheckFailCode, consts.ValidatorParamsCheckFailMsg, wrongParam)
	c.Abort()
}

// 系统执行代码错误
func ErrorSystem(c *gin.Context, msg string, data interface{}) {
	ReturnJson(c, http.StatusInternalServerError, consts.ServerOccurredErrorCode, consts.ServerOccurredErrorMsg+msg, data)
	c.Abort()
}

// token 参数校验错误
func TokenErrorParam(c *gin.Context, wrongParam interface{}) {
	ReturnJson(c, http.StatusUnauthorized, consts.ValidatorParamsCheckFailCode, consts.ValidatorParamsCheckFailMsg, wrongParam)
	c.Abort()
}

// session 校验错误
func SessionErrorParam(c *gin.Context, wrongParam interface{}) {
	ReturnJson(c, http.StatusUnauthorized, consts.SessionErrorCode, consts.SessionErrorMsg, wrongParam)
	c.Abort()
}

// ValidatorError 翻译表单参数验证器出现的校验错误
// func ValidatorError(c *gin.Context, err error) {
// 	if errs, ok := err.(validator.ValidationErrors); ok {
// 		wrongParam := validator_translation.RemoveTopStruct(errs.Translate(validator_translation.Trans))
// 		ReturnJson(c, http.StatusBadRequest, consts.ValidatorParamsCheckFailCode, consts.ValidatorParamsCheckFailMsg, wrongParam)
// 	} else {
// 		errStr := err.Error()
// 		// multipart:nextpart:eof 错误表示验证器需要一些参数，但是调用者没有提交任何参数
// 		if strings.ReplaceAll(strings.ToLower(errStr), " ", "") == "multipart:nextpart:eof" {
// 			ReturnJson(c, http.StatusBadRequest, consts.ValidatorParamsCheckFailCode, consts.ValidatorParamsCheckFailMsg, gin.H{"tips": my_errors.ErrorNotAllParamsIsBlank})
// 		} else {
// 			ReturnJson(c, http.StatusBadRequest, consts.ValidatorParamsCheckFailCode, consts.ValidatorParamsCheckFailMsg, gin.H{"tips": errStr})
// 		}
// 	}
// 	c.Abort()
// }

func ValidatorError(err error, obj interface{}) string {
	getObj := reflect.TypeOf(obj)
	// log.Printf(err.(validator.ValidationErrors).Error())
	if errs, ok := err.(validator.ValidationErrors); ok {
		for _, e := range errs {
			if f, exist := getObj.Elem().FieldByName(e.Field()); exist {
				return f.Tag.Get("msg")
			}
		}
	}
	return err.Error()
}
