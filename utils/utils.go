package utils

import (
	"fmt"
	"math/rand"
	"time"

	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
	"lsservice/app/model"

	"github.com/gin-gonic/gin"
	ua "github.com/mileusna/useragent"
	"github.com/xiaoqidun/qqwry"
)

// 获取客户端IP
func GetClientIp(ctx *gin.Context) string {
	ip := ctx.Request.Header.Get("X-Forwarded-For")
	if ip == "" {
		ip = ctx.ClientIP()
	}
	return ip
}

// 根据ip获取address
func GetAddr(ip string) (addr string) {
	// 从文件加载IP数据库
	if err := qqwry.LoadFile(variable.BasePath + paths.PublicDir + "assets/qqwry.ipdb"); err != nil {
		// fmt.Printf("%+v\n", err.Error())
		return ""
	}

	location, err := qqwry.QueryIP(ip)
	if err != nil {
		// fmt.Printf("%+v\n", err.Error())
		return ""
	}

	addr = location.City + location.District + " " + location.ISP

	return addr
}

// 获取 User Agent 值
func GetUserAgent(ctx *gin.Context) string {
	agent := ctx.Request.Header.Get("User-Agent")
	if agent == "" {
		agent = ctx.GetHeader("User-Agent")
	}
	return agent
}

// 获取设备信息
func GetInfo4UA(agent string) (os string, osVer string, browser string, browserVer string, device string) {
	ua := ua.Parse(agent)
	os = ua.OS
	osVer = ua.OSVersion
	browser = ua.Name
	browserVer = ua.Version
	device = ua.Device

	return os, osVer, browser, browserVer, device
}

// 获取请求的用户信息
func GetAuthInfo(ctx *gin.Context) (user *model.AuthInfoModel) {
	var authInfo model.AuthInfoModel

	ip := GetClientIp(ctx)
	agent := GetUserAgent(ctx)
	os, osVer, browser, browserVer, device := GetInfo4UA(agent)
	addr := GetAddr(ip)

	authInfo.IpAddr = ip
	authInfo.Location = addr
	authInfo.Browser = browser
	authInfo.BrowserVersion = browserVer
	authInfo.Os = os
	authInfo.OsVersion = osVer
	authInfo.Device = device

	return &authInfo
}

// 6位验证码
func GenVerifyCode() string {
	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))
	vcode := fmt.Sprintf("%06v", rnd.Int31n(1000000))

	return vcode
}

// 时间 string 转 time.Time
func String2Time(date string) *time.Time {
	loc, _ := time.LoadLocation("Local")
	the_time, err := time.ParseInLocation("2006-01-02 15:04:05", date, loc)
	if err != nil {
		return nil
	}

	return &the_time
}

// 数组包含某项
func IsContain(items []string, item string) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}
