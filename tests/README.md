# Go Test Cases for LS-Service

This directory contains test cases for the LS-Service project. The tests are written using Go's standard testing package along with some additional testing libraries.

## Test Structure

The test files are organized to mirror the structure of the main codebase:

- `utils/md5_encrypt/md5_encrypt_test.go` - Tests for MD5 encryption utilities
- `utils/gstr/gstr_test.go` - Tests for string utilities
- `utils/aes_encrypt/aes_encrypt_test.go` - Tests for AES encryption utilities
- `app/service/auth_test.go` - Tests for authentication service
- `app/controller/auth_test.go` - Tests for authentication controller

## Dependencies

The tests use the following external packages:

- `github.com/stretchr/testify/assert` - For assertions
- `github.com/stretchr/testify/mock` - For mocking
- `github.com/DATA-DOG/go-sqlmock` - For mocking database interactions

Make sure to install these dependencies before running the tests:

```bash
go get github.com/stretchr/testify/assert
go get github.com/stretchr/testify/mock
go get github.com/DATA-DOG/go-sqlmock
```

## Running Tests

### Running All Tests

To run all tests in the project:

```bash
go test ./...
```

### Running Tests for a Specific Package

To run tests for a specific package:

```bash
go test ./utils/md5_encrypt
go test ./utils/gstr
go test ./utils/aes_encrypt
go test ./app/service
go test ./app/controller
```

### Running a Specific Test

To run a specific test:

```bash
go test ./utils/md5_encrypt -run TestMD5
go test ./app/service -run TestAuthService_Login
```

### Running Tests with Coverage

To run tests with coverage:

```bash
go test ./... -cover
```

To generate a coverage report:

```bash
go test ./... -coverprofile=coverage.out
go tool cover -html=coverage.out
```

## Writing New Tests

When writing new tests, follow these guidelines:

1. Create a test file with the same name as the file being tested, but with `_test.go` suffix
2. Use table-driven tests where appropriate
3. Mock external dependencies
4. Use descriptive test names
5. Test both success and failure cases
6. Test edge cases

Example of a table-driven test:

```go
func TestSomeFunction(t *testing.T) {
    tests := []struct {
        name     string
        input    string
        expected string
    }{
        {
            name:     "Test case 1",
            input:    "input1",
            expected: "expected1",
        },
        {
            name:     "Test case 2",
            input:    "input2",
            expected: "expected2",
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := SomeFunction(tt.input)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

## Mocking

For service and controller tests, use mocks to isolate the unit being tested from its dependencies. The tests in this project demonstrate how to:

1. Mock database interactions using `go-sqlmock`
2. Mock service dependencies using `testify/mock`
3. Set up and tear down test environments

## Troubleshooting

If you encounter issues with the tests:

1. Make sure all dependencies are installed
2. Check that the test is correctly set up with proper mocks
3. Verify that the test is correctly cleaning up after itself
4. Check for any environment-specific configurations that might be needed

For database-related tests, ensure that the SQL queries in the mocks match exactly what the code is executing, including whitespace and parameter placeholders.
