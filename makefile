# 定义 makefile 的命名列表, 只需要将外部调用的公布在这里即可
.PHONY:  dev-api build-api   dev-web build-web build-cli help

buildPath=./build/
# 设置 cmd/api/main.go 入口文件编译后的可执行文件名
apiBinName="lsservice-api.linux64"
apiBinPath=${buildPath}${apiBinName}

# 设置 cmd/web/main.go 入口文件编译后的可执行文件名
webBinName="lsservice-web.linux64"
webBinPath=${buildPath}${webBinName}

# 设置 cmd/cli/main.go 入口文件编译后的可执行文件名
# cliBinName="lsservice-cli.linux64"
# cliBinPath=${buildPath}${cliBinName}

# 热更新
dev-api:
	air -c .air-api.conf -d
dev-web:
	air -c .air-web.conf -d

# 统一设置编译的目标平台公共参数
setup:
	# go env -w GOARCH=amd64
	# go env -w GOOS=linux
	go env -w CGO_ENABLED=0
	go env -w GO111MODULE=on
	# go env -w GOPROXY=https://goproxy.cn,direct
	# go mod tidy

build-api:clean-api build-api-bin
build-api-bin:
	GOOS=linux
	GOARCH=amd64
	go build -o ${apiBinPath}    -ldflags "-w -s"  -trimpath  ./cmd/api/main.go

build-web:clean-web build-web-bin
build-web-bin:
	GOOS=linux
	GOARCH=amd64
	go build -o ${webBinPath}   -ldflags "-w -s"  -trimpath  ./cmd/web/main.go

# build-cli:all clean-cli build-cli-bin
# build-cli-bin:
# 	go build -o ${cliBinPath}   -ldflags "-w -s"  -trimpath  ./cmd/cli/main.go

# 编译前清理可能已经存在的旧文件
clean-api:
	@if [ -f ${apiBinPath} ] ; then rm -rf ${apiBinPath} ; fi
clean-web:
	@if [ -f ${webBinPath} ] ; then rm -rf ${webBinPath} ; fi
# clean-cli:
# 	@if [ -f ${cliBinPath} ] ; then rm -rf ${cliBinPath} ; fi

help:
	@echo "make help 查看编译命令列表"
	@echo "make dev-api 用 air 调试 cmd/api/main.go 入口文件"
	@echo "make build-api  编译 cmd/api/main.go 入口文件"
	@echo "make dev-web 用 air 调试 cmd/web/main.go 入口文件"
	@echo "make build-web  编译 cmd/web/main.go 入口文件 "
#	@echo "make build-cli  编译 cmd/cli/main.go 入口文件"
