package routers

import (
	"net/http"

	"lsservice/app/controller"
	"lsservice/app/global/consts"
	"lsservice/app/global/variable"
	"lsservice/app/middleware/authorization"
	"lsservice/app/middleware/cors"
	"lsservice/app/middleware/logsave"
	"lsservice/utils/gin_release"

	"github.com/gin-contrib/pprof"
	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func InitManageRouter() *gin.Engine {
	var router *gin.Engine

	// 非调试模式（生产模式） 日志写到日志文件
	if !variable.ConfigYml.GetBool("AppDebug") {
		// 1.gin自行记录接口访问日志，不需要nginx，如果开启以下3行，那么请屏蔽第 34 行代码
		// gin.DisableConsoleColor()
		// f, _ := os.Create(variable.BasePath + paths.GinLogName)
		// gin.DefaultWriter = io.MultiWriter(f)

		//【生产模式】
		// 根据 gin 官方的说明：[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
		// 如果部署到生产环境，请使用以下模式：
		// 1.生产模式(release) 和开发模式的变化主要是禁用 gin 记录接口访问日志，
		// 2.go服务就必须使用nginx作为前置代理服务，这样也方便实现负载均衡
		// 3.如果程序发生 panic 等异常使用自定义的 panic 恢复中间件拦截、记录到日志
		router = gin_release.ReleaseRouter()
	} else {
		// 调试模式，开启 pprof 包，便于开发阶段分析程序性能
		router = gin.Default()
		pprof.Register(router)
	}

	// 设置可信任的代理服务器列表,gin (2021-11-24发布的v1.7.7版本之后出的新功能)
	if variable.ConfigYml.GetInt("HttpServer.TrustProxies.IsOpen") == 1 {
		if err := router.SetTrustedProxies(variable.ConfigYml.GetStringSlice("HttpServer.TrustProxies.ProxyServerList")); err != nil {
			variable.ZapLog.Error(consts.GinSetTrustProxyError, zap.Error(err))
		}
	} else {
		_ = router.SetTrustedProxies(nil)
	}

	// 根据配置进行设置跨域
	if variable.ConfigYml.GetBool("HttpServer.AllowCrossDomain") {
		// 跨域处理(要在路由组之前全局使用「跨域中间件」, 否则OPTIONS会返回404)
		router.Use(cors.Next())
	}

	// 设置 Session
	store := cookie.NewStore([]byte(variable.ConfigYml.GetString("Session.Secret")))
	store.Options(sessions.Options{
		Path:   "/",
		MaxAge: int(30 * 60), // 30 min
	})
	router.Use(sessions.Sessions(variable.ConfigYml.GetString("Session.SysName"), store))

	// 设置静态资源路由
	// router.Static("/resource", "./public/resource")
	// router.StaticFile("/favicon.ico", "./public/resource/images/favicon.ico")

	router.GET("/", func(context *gin.Context) {
		context.String(http.StatusOK, "HelloWorld,这是管理端API模块")
	})

	vAuth := router.Group("auth")
	{
		// 登录相关
		vAuth.Use(logsave.Auth())
		{
			vAuth.POST("/login", controller.Auth.Login)
			vAuth.POST("/logout", controller.Auth.Logout)
		}
		vAuth.GET("/captcha", controller.Auth.Captcha)
	}

	router.Use(authorization.CheckSessionAuth(), logsave.Operation())
	// router.Use(logsave.Operation())
	{
		router.GET("/logincheck", controller.Auth.LoginCheck)
		router.GET("/server", controller.Server.Info)

		vManage := router.Group("manage")
		{
			// 用户管理
			user := vManage.Group("users")
			{
				user.GET("", controller.User.List)
				user.GET("/:id", controller.User.Detail)
				user.POST("", controller.User.Insert)
				user.PUT("/:id", controller.User.Update)
				user.DELETE("/:ids", controller.User.Delete)
				user.PATCH("/state/:id", controller.User.State)
				user.PATCH("/resetpwd/:id", controller.User.ResetPwd)
				user.PATCH("/updatepwd/:id", controller.User.UpdatePwd)
				user.GET("/checkuser", controller.User.CheckUser)
				user.POST("/import", controller.User.Import)
				user.GET("/export", controller.User.Export)
			}

			// 角色管理
			role := vManage.Group("roles")
			{
				role.GET("", controller.Role.List)
				role.GET("/:id", controller.Role.Detail)
				role.POST("", controller.Role.Insert)
				role.PUT("/:id", controller.Role.Update)
				role.DELETE("/:ids", controller.Role.Delete)
				role.PATCH("/state/:id", controller.Role.State)
				role.PUT("/data/:id", controller.Role.DataScope)
				role.GET("/simple", controller.Role.ListSimple)
				role.GET("/export", controller.Role.Export)
			}

			// 菜单管理
			menu := vManage.Group("menus")
			{
				menu.GET("", controller.Menu.Tree)
				menu.GET("/:id", controller.Menu.Detail)
				menu.POST("", controller.Menu.Insert)
				menu.PUT("/:id", controller.Menu.Update)
				menu.DELETE("/:ids", controller.Menu.Delete)
				menu.PATCH("/visible/:id", controller.Menu.Visible)
				menu.GET("/tree", controller.Menu.TreeSimple)
				menu.GET("/role/:id", controller.Menu.TreeByRole)
			}

			// 部门管理
			dept := vManage.Group("depts")
			{
				dept.GET("", controller.Dept.Tree)
				dept.GET("/:id", controller.Dept.Detail)
				dept.POST("", controller.Dept.Insert)
				dept.PUT("/:id", controller.Dept.Update)
				dept.DELETE("/:ids", controller.Dept.Delete)
				dept.PATCH("/state/:id", controller.Dept.State)
				dept.GET("/tree", controller.Dept.TreeSimple)
				dept.GET("/export", controller.Dept.Export)
			}

			// 岗位管理
			post := vManage.Group("posts")
			{
				post.GET("", controller.Post.List)
				post.GET("/:id", controller.Post.Detail)
				post.POST("", controller.Post.Insert)
				post.PUT("/:id", controller.Post.Update)
				post.DELETE("/:ids", controller.Post.Delete)
				post.PATCH("/state/:id", controller.Post.State)
				post.GET("/simple", controller.Post.ListSimple)
				post.GET("/export", controller.Post.Export)
			}

			// 字典类型管理
			dictType := vManage.Group("dicttypes")
			{
				dictType.GET("", controller.DictType.List)
				dictType.GET("/:id", controller.DictType.Detail)
				dictType.POST("", controller.DictType.Insert)
				dictType.PUT("/:id", controller.DictType.Update)
				dictType.DELETE("/:ids", controller.DictType.Delete)
				dictType.PATCH("/state/:id", controller.DictType.State)
				dictType.GET("/export", controller.DictType.Export)
			}

			// 字典数据管理
			dictData := vManage.Group("dictdatas")
			{
				dictData.GET("", controller.DictData.List)
				dictData.GET("/:id", controller.DictData.Detail)
				dictData.POST("", controller.DictData.Insert)
				dictData.PUT("/:id", controller.DictData.Update)
				dictData.DELETE("/:ids", controller.DictData.Delete)
				dictData.PATCH("/state/:id", controller.DictData.State)
				dictData.GET("/lists", controller.DictData.ListByTypes)
				dictData.GET("/export", controller.DictData.Export)
			}

			// 参数管理
			config := vManage.Group("configs")
			{
				config.GET("", controller.Config.List)
				config.GET("/:id", controller.Config.Detail)
				config.POST("", controller.Config.Insert)
				config.PUT("/:id", controller.Config.Update)
				config.DELETE("/:ids", controller.Config.Delete)
				config.GET("/export", controller.Config.Export)
			}

			// 通知公告
			notice := vManage.Group("notices")
			{
				notice.GET("", controller.Notice.List)
				notice.GET("/:id", controller.Notice.Detail)
				notice.POST("", controller.Notice.Insert)
				notice.PUT("/:id", controller.Notice.Update)
				notice.DELETE("/:ids", controller.Notice.Delete)
				notice.PATCH("/state/:id", controller.Notice.State)
			}

			// 在线用户
			logOnline := vManage.Group("logonlines")
			{
				logOnline.GET("", controller.LogOnline.List)
				logOnline.PUT("/offline/:id", controller.LogOnline.Offline)
				logOnline.DELETE("", controller.LogOnline.Empty)
			}

			// 登录日志
			logAuth := vManage.Group("logauths")
			{
				logAuth.GET("", controller.LogAuth.List)
				logAuth.GET("/:id", controller.LogAuth.Detail)
				logAuth.DELETE("/:ids", controller.LogAuth.Delete)
				logAuth.DELETE("", controller.LogAuth.Empty)
				logAuth.GET("/export", controller.LogAuth.Export)
			}

			// 操作日志
			logOper := vManage.Group("logopers")
			{
				logOper.GET("", controller.LogOper.List)
				logOper.GET("/:id", controller.LogOper.Detail)
				logOper.DELETE("/:ids", controller.LogOper.Delete)
				logOper.DELETE("", controller.LogOper.Empty)
				logOper.GET("/export", controller.LogOper.Export)
			}

			// 上传操作
			upload := vManage.Group("upload")
			{
				upload.POST("/img", controller.Upload.UploadImg)
				upload.POST("/file", controller.Upload.UploadFile)
			}
		}

	}

	return router
}
