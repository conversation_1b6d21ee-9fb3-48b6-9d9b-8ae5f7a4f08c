package routers

import (
	"net/http"

	"lsservice/app/global/consts"
	"lsservice/app/global/variable"
	"lsservice/app/middleware/cors"
	"lsservice/utils/gin_release"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func InitWebsiteRouter() *gin.Engine {
	var router *gin.Engine

	// 非调试模式（生产模式） 日志写到日志文件
	if !variable.ConfigYml.GetBool("AppDebug") {
		// 1.gin自行记录接口访问日志，不需要nginx，如果开启以下3行，那么请屏蔽第 34 行代码
		// gin.DisableConsoleColor()
		// f, _ := os.Create(variable.BasePath + variable.ConfigYml.GetString("Logs.GinLogName"))
		// gin.DefaultWriter = io.MultiWriter(f)

		//【生产模式】
		// 根据 gin 官方的说明：[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
		// 如果部署到生产环境，请使用以下模式：
		// 1.生产模式(release) 和开发模式的变化主要是禁用 gin 记录接口访问日志，
		// 2.go服务就必须使用nginx作为前置代理服务，这样也方便实现负载均衡
		// 3.如果程序发生 panic 等异常使用自定义的 panic 恢复中间件拦截、记录到日志
		router = gin_release.ReleaseRouter()
	} else {
		// 调试模式，开启 pprof 包，便于开发阶段分析程序性能
		router = gin.Default()
		pprof.Register(router)
	}

	// 设置可信任的代理服务器列表,gin (2021-11-24发布的v1.7.7版本之后出的新功能)
	if variable.ConfigYml.GetInt("HttpServer.TrustProxies.IsOpen") == 1 {
		if err := router.SetTrustedProxies(variable.ConfigYml.GetStringSlice("HttpServer.TrustProxies.ProxyServerList")); err != nil {
			variable.ZapLog.Error(consts.GinSetTrustProxyError, zap.Error(err))
		}
	} else {
		_ = router.SetTrustedProxies(nil)
	}

	// 根据配置进行设置跨域
	if variable.ConfigYml.GetBool("HttpServer.AllowCrossDomain") {
		// 跨域处理(要在路由组之前全局使用「跨域中间件」, 否则OPTIONS会返回404)
		router.Use(cors.Next())
	}

	router.GET("/", func(context *gin.Context) {
		context.String(http.StatusOK, "HelloWorld,这是网站API模块")
	})

	// vWeb := router.Group("web")
	// {
	// 	// 登录相关
	// 	vWeb.POST("/login", controller.Web.Login)
	// 	vWeb.POST("/register", controller.Web.Register)
	// 	vWeb.POST("/logout", controller.Web.Logout)
	// 	vWeb.GET("/captcha", controller.Web.Captcha)
	// }

	return router
}
