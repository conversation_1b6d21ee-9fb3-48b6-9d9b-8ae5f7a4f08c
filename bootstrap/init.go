package boot

import (
	"log"
	"os"

	"lsservice/app/core/sys_log_hook"
	"lsservice/app/global/my_errors"
	"lsservice/app/global/paths"
	"lsservice/app/global/variable"
	"lsservice/utils/gorm_v2"
	"lsservice/utils/yml_config"
	"lsservice/utils/zap_factory"

	"github.com/bwmarrin/snowflake"
)

// 检查项目必须的非编译目录是否存在，避免编译后调用的时候缺失相关目录
func checkRequiredFolders() {
	// 1.检查配置文件是否存在
	if _, err := os.Stat(variable.BasePath + paths.ConfigFile); err != nil {
		log.Fatal(my_errors.ErrorsConfigYamlNotExists + err.Error())
	}
	if _, err := os.Stat(variable.BasePath + paths.Gormv2File); err != nil {
		log.Fatal(my_errors.ErrorsConfigGormNotExists + err.Error())
	}
	// 2.检查public目录是否存在
	if _, err := os.Stat(variable.BasePath + paths.PublicDir); err != nil {
		log.Fatal(my_errors.ErrorsPublicNotExists + err.Error())
	}
	// 3.检查storage/logs 目录是否存在
	if _, err := os.Stat(variable.BasePath + paths.LogDir); err != nil {
		log.Fatal(my_errors.ErrorsStorageLogsNotExists + err.Error())
	}
	// 4.检查storage/app 目录是否存在
	if _, err := os.Stat(variable.BasePath + paths.AppDir); err != nil {
		log.Fatal(my_errors.ErrorsStorageAppNotExists + err.Error())
	}
	// 5.自动创建软连接、更好的管理静态资源
	if _, err := os.Stat(variable.BasePath + paths.SymApp); err == nil {
		if err := os.RemoveAll(variable.BasePath + paths.SymApp); err != nil {
			log.Fatal(my_errors.ErrorsSoftLinkDeleteFail + err.Error())
		}
	}
	if err := os.Symlink(variable.BasePath+paths.AppDir, variable.BasePath+paths.SymApp); err != nil {
		log.Fatal(my_errors.ErrorsSoftLinkCreateFail + err.Error())
	}
}

func init() {
	// 1.初始化 项目根路径，参见 variable 常量包，相关路径：app\global\variable\variable.go

	// 2.检查配置文件以及日志目录等非编译性的必要条件
	checkRequiredFolders()

	// 3.初始化表单参数验证器

	// 4.启动针对配置文件(confgi.yml、gorm_v2.yml)变化的监听， 配置文件操作指针，初始化为全局变量
	variable.ConfigYml = yml_config.CreateYamlFactory()
	variable.ConfigYml.ConfigFileChangeListen()
	// config>gorm_v2.yml 启动文件变化监听事件
	variable.ConfigGormv2Yml = variable.ConfigYml.Clone("gorm_v2")
	variable.ConfigGormv2Yml.ConfigFileChangeListen()

	// 5.初始化全局日志句柄，并载入日志钩子处理函数
	variable.ZapLog = zap_factory.CreateZapFactory(sys_log_hook.ZapLogHandler)

	// 6.根据配置初始化 gorm postgresql 全局 *gorm.Db
	if variable.ConfigGormv2Yml.GetInt("Gormv2.PostgreSql.IsInitGolobalGormPostgreSql") == 1 {
		if dbPostgre, err := gorm_v2.GetOnePostgreSqlClient(); err != nil {
			log.Fatal(my_errors.ErrorsGormInitFail + err.Error())
		} else {
			variable.GormDbPostgreSql = dbPostgre
		}
	}

	// 7.初始化雪花算法
	variable.SnowFlakeNode, _ = snowflake.NewNode(variable.ConfigYml.GetInt64("SnowFlake.SnowFlakeMachineId"))
}
