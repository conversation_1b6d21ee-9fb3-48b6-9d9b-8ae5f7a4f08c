# Golang 后台服务

## 开发插件

1. [gofumpt(代码格式化)](https://github.com/mvdan/gofumpt)

```shell
go install mvdan.cc/gofumpt@latest
```

vscode 添加设置项 `settings.json`

```json
{
  "gopls": {
    "formatting.gofumpt": true,
  }
}
```

2-1. [Air(热更新)](https://github.com/cosmtrek/air)

```shell
go install github.com/cosmtrek/air@latest

# 启动
air -c .air.conf -d
```

2-2. [<PERSON><PERSON>(热更新)](https://github.com/makiuchi-d/arelo)

```shell
go install github.com/makiuchi-d/arelo@latest

# 快速启动
arelo -p '**/*.go' -i '**/.*' -i '**/*_test.go' -- go run .
```

## 插件

1. [Gin(Web框架)](https://gin-gonic.com/zh-cn/)

```shell
go get -u github.com/gin-gonic/gin
```

2. [GORM(ORM框架)](https://gorm.io/zh_CN/)

```shell
go get -u gorm.io/gorm
# PostgreSQL
go get -u gorm.io/driver/postgres
```

3. [Casbin(权限管理)](https://casbin.org/zh/)

```shell
go get github.com/casbin/casbin/v2
```

4. [Viper(配置管理)](https://github.com/spf13/viper)

```shell
go get github.com/spf13/viper
```

5. [zap(日志管理)](https://github.com/uber-go/zap)

```shell
go get -u go.uber.org/zap
```

6. [UserAgent(HTTP请求信息)](https://github.com/mileusna/useragent)

```shell
go get github.com/mileusna/useragent
```

7. [snowflake(雪花算法)](https://github.com/bwmarrin/snowflake)

```shell
go get github.com/bwmarrin/snowflake
```

8. [NanoID(唯一ID,替代UUID)](https://github.com/jaevor/go-nanoid)

```shell
go get github.com/jaevor/go-nanoid
```
