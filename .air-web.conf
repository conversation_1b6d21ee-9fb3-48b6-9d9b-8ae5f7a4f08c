# [Air](https://github.com/cosmtrek/air) TOML 格式的配置文件

# 工作目录
# 使用 . 或绝对路径，请注意 `tmp_dir` 目录必须在 `root` 目录下
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  # 只需要写你平常编译使用的shell命令。你也可以使用 `make`
  # Windows平台示例: cmd = "go build -o ./tmp/main.exe ."
  cmd = "go build -o ./tmp/main ./cmd/web/"
  # 由`cmd`命令得到的二进制文件名
  # Windows平台示例：bin = "tmp/main.exe"
  bin = "tmp/main"
  # 自定义执行程序的命令，可以添加额外的编译标识例如添加 GIN_MODE=release
  # Windows平台示例：full_bin = "./tmp/main.exe"
  # Linux平台示例：full_bin = "APP_ENV=dev APP_USER=air ./tmp/main"
  full_bin = "./tmp/main"
  # 监听以下指定目录的文件.
  include_dir = []
  # 监听以下文件扩展名的文件.
  include_ext = ["go", "tpl", "tmpl", "html"]
  # 忽略这些文件扩展名或目录
  # exclude_dir = ["assets", "tmp", "vendor", "testdata"]
  exclude_dir = ["database", "docs", "storage", "tmp"]
  # 排除以下文件
  exclude_file = []
  exclude_regex = ["_test.go"]
  exclude_unchanged = false
  # 如果文件更改过于频繁，则没有必要在每次更改时都触发构建。可以设置触发构建的延迟时间
  delay = 1000 # ms
  follow_symlink = false
  kill_delay = "0s"
  # air的日志文件名，该日志文件放置在你的`tmp_dir`中
  log = "build-errors.log"
  send_interrupt = false
  # 发生构建错误时，停止运行旧的二进制文件。
  stop_on_error = true

[color]
  # 自定义每个部分显示的颜色。如果找不到颜色，使用原始的应用程序日志。
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  # 显示日志时间
  time = false

[misc]
  # 退出时删除tmp目录
  clean_on_exit = false

[screen]
  clear_on_rebuild = false
