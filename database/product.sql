CREATE TABLE "tb_category" (
  "category_id" serial8,
  "category_code" varchar(30) NOT NULL,
  "category_name" varchar(50),
  "state" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz,
  "remark" varchar(255) DEFAULT NULL,
  PRIMARY KEY ("category_id")
);
COMMENT ON COLUMN "tb_category"."category_id" IS '种类主键';
COMMENT ON COLUMN "tb_category"."category_code" IS '种类编码';
COMMENT ON COLUMN "tb_category"."category_name" IS '种类名称';
COMMENT ON COLUMN "tb_category"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "tb_category"."created_by" IS '创建者';
COMMENT ON COLUMN "tb_category"."created_at" IS '创建时间';
COMMENT ON COLUMN "tb_category"."updated_by" IS '更新者';
COMMENT ON COLUMN "tb_category"."updated_at" IS '更新时间';
COMMENT ON COLUMN "tb_category"."remark" IS '备注';
COMMENT ON TABLE "tb_category" IS '品种类型信息表';

CREATE TABLE "tb_custom_data" (
  "custom_code" serial8,
  "custom_label" varchar(30) DEFAULT '',
  "custom_value" varchar(2) NOT NULL,
  "custom_type" varchar(50) DEFAULT '',
  "price" decimal(10,2) DEFAULT 0,
  "sort" int2 DEFAULT 0,
  "is_default" char(1) DEFAULT '0',
  "state" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz,
  "remark" varchar(255) DEFAULT NULL,
  PRIMARY KEY ("custom_code")
);
COMMENT ON COLUMN "tb_custom_data"."custom_code" IS '定制编码';
COMMENT ON COLUMN "tb_custom_data"."custom_label" IS '定制标签';
COMMENT ON COLUMN "tb_custom_data"."custom_value" IS '定制键值';
COMMENT ON COLUMN "tb_custom_data"."custom_type" IS '定制类型';
COMMENT ON COLUMN "tb_custom_data"."price" IS '差价';
COMMENT ON COLUMN "tb_custom_data"."sort" IS '排序';
COMMENT ON COLUMN "tb_custom_data"."is_default" IS '是否默认:0否,1是';
COMMENT ON COLUMN "tb_custom_data"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "tb_custom_data"."created_by" IS '创建者';
COMMENT ON COLUMN "tb_custom_data"."created_at" IS '创建时间';
COMMENT ON COLUMN "tb_custom_data"."updated_by" IS '更新者';
COMMENT ON COLUMN "tb_custom_data"."updated_at" IS '更新时间';
COMMENT ON COLUMN "tb_custom_data"."remark" IS '备注';
COMMENT ON TABLE "tb_custom_data" IS '定制数据信息表';

CREATE TABLE "tb_custom_type" (
  "custom_id" serial8,
  "custom_name" varchar(30) DEFAULT '',
  "custom_type" varchar(50) NOT NULL,
  "state" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz,
  "remark" varchar(255) DEFAULT NULL,
  PRIMARY KEY ("custom_id")
);
COMMENT ON COLUMN "tb_custom_type"."custom_id" IS '定制主键';
COMMENT ON COLUMN "tb_custom_type"."custom_name" IS '定制名称';
COMMENT ON COLUMN "tb_custom_type"."custom_type" IS '定制类型';
COMMENT ON COLUMN "tb_custom_type"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "tb_custom_type"."created_by" IS '创建者';
COMMENT ON COLUMN "tb_custom_type"."created_at" IS '创建时间';
COMMENT ON COLUMN "tb_custom_type"."updated_by" IS '更新者';
COMMENT ON COLUMN "tb_custom_type"."updated_at" IS '更新时间';
COMMENT ON COLUMN "tb_custom_type"."remark" IS '备注';
COMMENT ON TABLE "tb_custom_type" IS '定制类型信息表';

CREATE TABLE "tb_produce" (
  "produce_id" serial8,
  "product_id" int8 NOT NULL,
  "number" int4 NOT NULL,
  "produce_at" timestamptz,
  PRIMARY KEY ("produce_id")
);
COMMENT ON COLUMN "tb_produce"."produce_id" IS '生产主键';
COMMENT ON COLUMN "tb_produce"."product_id" IS '产品主键';
COMMENT ON COLUMN "tb_produce"."number" IS '数量';
COMMENT ON COLUMN "tb_produce"."produce_at" IS '生产日期';
COMMENT ON TABLE "tb_produce" IS '生产信息表';

CREATE TABLE "tb_product" (
  "product_id" serial8,
  "shop_id" int8 NOT NULL,
  "variety_id" int8 NOT NULL,
  "price" decimal(10,2),
  "shelve" char(1) DEFAULT '0',
  "expire_time" int4,
  PRIMARY KEY ("product_id")
);
COMMENT ON COLUMN "tb_product"."product_id" IS '产品主键';
COMMENT ON COLUMN "tb_product"."shop_id" IS '店铺主键';
COMMENT ON COLUMN "tb_product"."variety_id" IS '品种主键';
COMMENT ON COLUMN "tb_product"."price" IS '价钱';
COMMENT ON COLUMN "tb_product"."shelve" IS '上架:0是,1否';
COMMENT ON COLUMN "tb_product"."expire_time" IS '到期时间(天)';
COMMENT ON TABLE "tb_product" IS '产品信息表';

CREATE TABLE "tb_rela_category:custom" (
  "category_id" int8 NOT NULL,
  "custom_id" int8 NOT NULL,
  PRIMARY KEY ("category_id", "custom_id")
);
COMMENT ON COLUMN "tb_rela_category:custom"."category_id" IS '种类主键';
COMMENT ON COLUMN "tb_rela_category:custom"."custom_id" IS '定制主键';
COMMENT ON TABLE "tb_rela_category:custom" IS '种类和定制关联表: 种类1-N定制';

CREATE TABLE "tb_rela_product:category" (
  "product_id" int8 NOT NULL,
  "category_id" int8 NOT NULL,
  PRIMARY KEY ("product_id", "category_id")
);
COMMENT ON COLUMN "tb_rela_product:category"."product_id" IS '产品主键';
COMMENT ON COLUMN "tb_rela_product:category"."category_id" IS '种类主键';
COMMENT ON TABLE "tb_rela_product:category" IS '产品和种类关联表: 产品1-N种类';

CREATE TABLE "tb_sys_shop" (
  "shop_id" serial2,
  "shop_name" varchar(50) NOT NULL,
  "status" char(1) DEFAULT '0',
  "logo" varchar(100) DEFAULT '',
  "address" varchar(255) DEFAULT '',
  "city_code" int4 DEFAULT NULL,
  "longitude" float4 DEFAULT 0,
  "latitude" float4 DEFAULT 0,
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz,
  "remark" varchar(255) DEFAULT NULL,
  PRIMARY KEY ("shop_id")
);
COMMENT ON COLUMN "tb_sys_shop"."shop_id" IS '店铺主键';
COMMENT ON COLUMN "tb_sys_shop"."shop_name" IS '店铺名称';
COMMENT ON COLUMN "tb_sys_shop"."status" IS '店铺状态:0正常,1停业';
COMMENT ON COLUMN "tb_sys_shop"."logo" IS '店标';
COMMENT ON COLUMN "tb_sys_shop"."address" IS '地址';
COMMENT ON COLUMN "tb_sys_shop"."city_code" IS '城市代码';
COMMENT ON COLUMN "tb_sys_shop"."longitude" IS '经度';
COMMENT ON COLUMN "tb_sys_shop"."latitude" IS '纬度';
COMMENT ON COLUMN "tb_sys_shop"."created_by" IS '创建者';
COMMENT ON COLUMN "tb_sys_shop"."created_at" IS '创建时间';
COMMENT ON COLUMN "tb_sys_shop"."updated_by" IS '更新者';
COMMENT ON COLUMN "tb_sys_shop"."updated_at" IS '更新时间';
COMMENT ON COLUMN "tb_sys_shop"."remark" IS '备注';
COMMENT ON TABLE "tb_sys_shop" IS '店铺信息表';

CREATE TABLE "tb_variety" (
  "variety_id" serial8,
  "variety_code" varchar(30) NOT NULL,
  "variety_name" varchar(50) DEFAULT '',
  "cover" varchar(100) DEFAULT '',
  "state" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz,
  "remark" varchar(255) DEFAULT NULL,
  PRIMARY KEY ("variety_id")
);
COMMENT ON COLUMN "tb_variety"."variety_id" IS '产品主键';
COMMENT ON COLUMN "tb_variety"."variety_code" IS '产品编码';
COMMENT ON COLUMN "tb_variety"."variety_name" IS '产品名称';
COMMENT ON COLUMN "tb_variety"."cover" IS '封面路径';
COMMENT ON COLUMN "tb_variety"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "tb_variety"."created_by" IS '创建者';
COMMENT ON COLUMN "tb_variety"."created_at" IS '创建时间';
COMMENT ON COLUMN "tb_variety"."updated_by" IS '更新者';
COMMENT ON COLUMN "tb_variety"."updated_at" IS '更新时间';
COMMENT ON COLUMN "tb_variety"."remark" IS '备注';
COMMENT ON TABLE "tb_variety" IS '品种信息表';

CREATE TABLE "tb_variety_image" (
  "image_id" serial8,
  "variety_id" int8 NOT NULL DEFAULT NULL,
  "src" varchar(100) DEFAULT NULL,
  PRIMARY KEY ("image_id", "variety_id")
);
COMMENT ON COLUMN "tb_variety_image"."image_id" IS '产品图片主键';
COMMENT ON COLUMN "tb_variety_image"."variety_id" IS '品种主键';
COMMENT ON COLUMN "tb_variety_image"."src" IS '图像路径';
COMMENT ON TABLE "tb_variety_image" IS '品种图像信息表';

