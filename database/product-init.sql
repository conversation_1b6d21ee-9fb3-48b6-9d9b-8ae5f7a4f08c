-- 创建模式
CREATE SCHEMA "s_market";

-- ----------------------------
-- 1、产品种类信息表
-- ----------------------------
CREATE TABLE IF NOT EXISTS "s_market"."tb_category" (
  "category_id" serial PRIMARY KEY,
  "category_code" varchar(30) NOT NULL,
  "category_name" varchar(50) DEFAULT '',
  "state" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_market"."tb_category"."category_id" IS '种类主键';
COMMENT ON COLUMN "s_market"."tb_category"."category_code" IS '种类编码';
COMMENT ON COLUMN "s_market"."tb_category"."category_name" IS '种类名称';
COMMENT ON COLUMN "s_market"."tb_category"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "s_market"."tb_category"."created_by" IS '创建者';
COMMENT ON COLUMN "s_market"."tb_category"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_market"."tb_category"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_market"."tb_category"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_market"."tb_category"."remark" IS '备注';
COMMENT ON TABLE "s_market"."tb_category" IS '产品种类信息表';

-- ----------------------------
-- 2、产品信息表
-- ----------------------------
CREATE TABLE IF NOT EXISTS "s_market"."tb_goods" (
  "goods_id" serial PRIMARY KEY,
  "goods_code" varchar(30) NOT NULL,
  "goods_name" varchar(50) DEFAULT '',
  "cover" varchar(100) DEFAULT '',
  "price" decimal(10,2) NOT NULL,
  "stock" int DEFAULT 0,
  "shelve" char(1) DEFAULT '0',
  "expire_time" int NOT NULL,
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_market"."tb_goods"."goods_id" IS '产品主键';
COMMENT ON COLUMN "s_market"."tb_goods"."goods_code" IS '产品编码';
COMMENT ON COLUMN "s_market"."tb_goods"."goods_name" IS '产品名称';
COMMENT ON COLUMN "s_market"."tb_goods"."cover" IS '封面路径';
COMMENT ON COLUMN "s_market"."tb_goods"."price" IS '价钱';
COMMENT ON COLUMN "s_market"."tb_goods"."stock" IS '库存';
COMMENT ON COLUMN "s_market"."tb_goods"."shelve" IS '上架:0是,1否';
COMMENT ON COLUMN "s_market"."tb_goods"."expire_time" IS '到期时间(天)';
COMMENT ON COLUMN "s_market"."tb_goods"."created_by" IS '创建者';
COMMENT ON COLUMN "s_market"."tb_goods"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_market"."tb_goods"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_market"."tb_goods"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_market"."tb_goods"."remark" IS '备注';
COMMENT ON TABLE "s_market"."tb_goods" IS '产品信息表';

-- ----------------------------
-- 3、店铺信息表
-- ----------------------------
CREATE TABLE IF NOT EXISTS "s_market"."tb_sys_shop" (
  "shop_id" serial2 PRIMARY KEY,
  "shop_name" varchar(50) NOT NULL,
  "state" char(1) DEFAULT '0',
  "logo" varchar(100) DEFAULT '',
  "address" varchar(255) DEFAULT '',
  "city_code" int DEFAULT NULL,
  "longitude" float DEFAULT 0,
  "latitude" float DEFAULT 0,
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_market"."tb_sys_shop"."shop_id" IS '店铺主键';
COMMENT ON COLUMN "s_market"."tb_sys_shop"."shop_name" IS '店铺名称';
COMMENT ON COLUMN "s_market"."tb_sys_shop"."status" IS '店铺状态:0正常,1停业';
COMMENT ON COLUMN "s_market"."tb_sys_shop"."logo" IS '店标';
COMMENT ON COLUMN "s_market"."tb_sys_shop"."address" IS '地址';
COMMENT ON COLUMN "s_market"."tb_sys_shop"."city_code" IS '城市代码';
COMMENT ON COLUMN "s_market"."tb_sys_shop"."longitude" IS '经度';
COMMENT ON COLUMN "s_market"."tb_sys_shop"."latitude" IS '纬度';
COMMENT ON COLUMN "s_market"."tb_sys_shop"."created_by" IS '创建者';
COMMENT ON COLUMN "s_market"."tb_sys_shop"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_market"."tb_sys_shop"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_market"."tb_sys_shop"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_market"."tb_sys_shop"."remark" IS '备注';
COMMENT ON TABLE "s_market"."tb_sys_shop" IS '店铺信息表';

-- ----------------------------
-- 4、定制类型信息表
-- ----------------------------
CREATE TABLE IF NOT EXISTS "s_market"."tb_custom_type" (
  "custom_id" serial PRIMARY KEY,
  "custom_name" varchar(30) DEFAULT '',
  "custom_type" varchar(50) NOT NULL UNIQUE,
  "state" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_market"."tb_custom_type"."custom_id" IS '定制主键';
COMMENT ON COLUMN "s_market"."tb_custom_type"."custom_name" IS '定制名称';
COMMENT ON COLUMN "s_market"."tb_custom_type"."custom_type" IS '定制类型';
COMMENT ON COLUMN "s_market"."tb_custom_type"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "s_market"."tb_custom_type"."created_by" IS '创建者';
COMMENT ON COLUMN "s_market"."tb_custom_type"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_market"."tb_custom_type"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_market"."tb_custom_type"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_market"."tb_custom_type"."remark" IS '备注';
COMMENT ON TABLE "s_market"."tb_custom_type" IS '定制类型信息表';

-- ----------------------------
-- 5、定制数据信息表
-- ----------------------------
CREATE TABLE IF NOT EXISTS "s_market"."tb_custom_data" (
  "custom_code" serial PRIMARY KEY,
  "custom_label" varchar(30) DEFAULT '',
  "custom_value" varchar(2) NOT NULL,
  "custom_type" varchar(50) DEFAULT '',
  "price" decimal(10,2) DEFAULT 0,
  "sort" int2 DEFAULT 0,
  "is_default" char(1) DEFAULT '0',
  "state" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_market"."tb_custom_data"."custom_code" IS '定制编码';
COMMENT ON COLUMN "s_market"."tb_custom_data"."custom_label" IS '定制标签';
COMMENT ON COLUMN "s_market"."tb_custom_data"."custom_value" IS '定制键值';
COMMENT ON COLUMN "s_market"."tb_custom_data"."custom_type" IS '定制类型';
COMMENT ON COLUMN "s_market"."tb_custom_data"."price" IS '价格';
COMMENT ON COLUMN "s_market"."tb_custom_data"."sort" IS '排序';
COMMENT ON COLUMN "s_market"."tb_custom_data"."is_default" IS '是否默认:0否,1是';
COMMENT ON COLUMN "s_market"."tb_custom_data"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "s_market"."tb_custom_data"."created_by" IS '创建者';
COMMENT ON COLUMN "s_market"."tb_custom_data"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_market"."tb_custom_data"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_market"."tb_custom_data"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_market"."tb_custom_data"."remark" IS '备注';
COMMENT ON TABLE "s_market"."tb_custom_data" IS '定制数据信息表';

-- ----------------------------
-- 6、生产信息表
-- ----------------------------
CREATE TABLE IF NOT EXISTS "s_market"."tb_produce" (
  "produce_id" serial PRIMARY KEY,
  "shop_id" int NOT NULL,
  "goods_id" int NOT NULL,
  "amount" int NOT NULL,
  "produced_at" date NOT NULL,
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_market"."tb_produce"."produce_id" IS '生产主键';
COMMENT ON COLUMN "s_market"."tb_produce"."shop_id" IS '店铺主键';
COMMENT ON COLUMN "s_market"."tb_produce"."goods_id" IS '产品主键';
COMMENT ON COLUMN "s_market"."tb_produce"."amount" IS '数量';
COMMENT ON COLUMN "s_market"."tb_produce"."produced_at" IS '生产日期';
COMMENT ON COLUMN "s_market"."tb_produce"."created_by" IS '创建者';
COMMENT ON COLUMN "s_market"."tb_produce"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_market"."tb_produce"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_market"."tb_produce"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_market"."tb_produce"."remark" IS '备注';
COMMENT ON TABLE "s_market"."tb_produce" IS '生产信息表';

-- ----------------------------
-- 7、采购商信息表
-- ----------------------------
CREATE TABLE IF NOT EXISTS "s_market"."tb_purchaser" (
  "purchaser_id" serial PRIMARY KEY,
  "purchaser_name" varchar(30) DEFAULT '',
  "purchaser_code" varchar(30) NOT NULL UNIQUE,
  "state" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_market"."tb_purchaser"."purchaser_id" IS '采购商主键';
COMMENT ON COLUMN "s_market"."tb_purchaser"."purchaser_name" IS '采购商名称';
COMMENT ON COLUMN "s_market"."tb_purchaser"."purchaser_code" IS '采购商编码';
COMMENT ON COLUMN "s_market"."tb_purchaser"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "s_market"."tb_purchaser"."created_by" IS '创建者';
COMMENT ON COLUMN "s_market"."tb_purchaser"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_market"."tb_purchaser"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_market"."tb_purchaser"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_market"."tb_purchaser"."remark" IS '备注';
COMMENT ON TABLE "s_market"."tb_purchaser" IS '采购商信息表';

-- ----------------------------
-- 8、采购信息表
-- ----------------------------
CREATE TABLE IF NOT EXISTS "s_market"."tb_purchase" (
  "purchase_id" serial PRIMARY KEY,
  "purchaser_id" int NOT NULL,
  "goods_id" int NOT NULL,
  "amount" int NOT NULL,
  "purchase_at" date NOT NULL,
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_market"."tb_purchase"."purchase_id" IS '采购主键';
COMMENT ON COLUMN "s_market"."tb_purchase"."purchaser_id" IS '采购商主键';
COMMENT ON COLUMN "s_market"."tb_purchase"."goods_id" IS '产品主键';
COMMENT ON COLUMN "s_market"."tb_purchase"."amount" IS '数量';
COMMENT ON COLUMN "s_market"."tb_purchase"."purchase_at" IS '采购日期';
COMMENT ON COLUMN "s_market"."tb_purchase"."created_by" IS '创建者';
COMMENT ON COLUMN "s_market"."tb_purchase"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_market"."tb_purchase"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_market"."tb_purchase"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_market"."tb_purchase"."remark" IS '备注';
COMMENT ON TABLE "s_market"."tb_purchase" IS '采购信息表';

-- ----------------------------
-- 8、经销商信息表
-- ----------------------------
CREATE TABLE IF NOT EXISTS "s_market"."tb_dealer" (
  "dealer_id" serial PRIMARY KEY,
  "dealer_name" varchar(30) DEFAULT '',
  "dealer_code" varchar(30) NOT NULL UNIQUE,
  "state" char(1) DEFAULT '0',
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_market"."tb_dealer"."dealer_id" IS '经销商主键';
COMMENT ON COLUMN "s_market"."tb_dealer"."dealer_name" IS '经销商名称';
COMMENT ON COLUMN "s_market"."tb_dealer"."dealer_code" IS '经销商编码';
COMMENT ON COLUMN "s_market"."tb_dealer"."state" IS '状态:0正常,1停用';
COMMENT ON COLUMN "s_market"."tb_dealer"."created_by" IS '创建者';
COMMENT ON COLUMN "s_market"."tb_dealer"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_market"."tb_dealer"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_market"."tb_dealer"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_market"."tb_dealer"."remark" IS '备注';
COMMENT ON TABLE "s_market"."tb_dealer" IS '经销商信息表';

-- ----------------------------
-- 9、销售信息表
-- ----------------------------
CREATE TABLE IF NOT EXISTS "s_market"."tb_sale" (
  "sale_id" serial PRIMARY KEY,
  "dealer_id" int NOT NULL,
  "goods_id" int NOT NULL,
  "amount" int NOT NULL,
  "sale_at" date NOT NULL,
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_market"."tb_sale"."sale_id" IS '销售主键';
COMMENT ON COLUMN "s_market"."tb_sale"."dealer_id" IS '经销商主键';
COMMENT ON COLUMN "s_market"."tb_sale"."goods_id" IS '产品主键';
COMMENT ON COLUMN "s_market"."tb_sale"."amount" IS '数量';
COMMENT ON COLUMN "s_market"."tb_sale"."sale_at" IS '销售日期';
COMMENT ON COLUMN "s_market"."tb_sale"."created_by" IS '创建者';
COMMENT ON COLUMN "s_market"."tb_sale"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_market"."tb_sale"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_market"."tb_sale"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_market"."tb_sale"."remark" IS '备注';
COMMENT ON TABLE "s_market"."tb_sale" IS '销售信息表';
