-- 创建模式
CREATE SCHEMA "s_website";

-- ----------------------------
-- 1、网站用户信息表
-- ----------------------------
-- DROP TABLE IF EXISTS "s_website"."tb_web_user";
CREATE TABLE IF NOT EXISTS "s_website"."tb_web_user" (
  "user_id" serial PRIMARY KEY,
  "user_name" varchar(30) NOT NULL UNIQUE,
  "nick_name" varchar(30) DEFAULT '',
  "user_type" char(2) DEFAULT '00',
  "mobile" varchar(11) DEFAULT '',
  "email" varchar(50) DEFAULT '',
  "gender" user_gender DEFAULT '0',
  "idcard_type" char(2) DEFAULT '',
  "idcard_code" varchar(30) DEFAULT '',
  "avatar" text DEFAULT '',
  "password" varchar(50) DEFAULT '',
  "salt" varchar(20) DEFAULT '',
  "state" char(1) DEFAULT '0',
  "del_flag" int2 DEFAULT 0,
  "login_count" int DEFAULT 0,
  "login_ip" inet DEFAULT NULL,
  "login_at" timestamptz,
  "pwd_updated_at" timestamptz,
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_website"."tb_web_user"."user_id" IS '用户ID';
COMMENT ON COLUMN "s_website"."tb_web_user"."user_name" IS '登录账号';
COMMENT ON COLUMN "s_website"."tb_web_user"."nick_name" IS '用户昵称';
COMMENT ON COLUMN "s_website"."tb_web_user"."user_type" IS '用户类型:00系统用户,01注册用户';
COMMENT ON COLUMN "s_website"."tb_web_user"."mobile" IS '手机号码';
COMMENT ON COLUMN "s_website"."tb_web_user"."email" IS '用户邮箱';
COMMENT ON COLUMN "s_website"."tb_web_user"."gender" IS '用户性别:0男,1女,2未知';
COMMENT ON COLUMN "s_website"."tb_web_user"."idcard_type" IS '证件类型:00身份证,01户口本,02外国人永久居留身份证,03护照,04士兵证,05港澳通行证,06台胞证,07其他,08军官证,9外籍护照,10文职证,11警官证';
COMMENT ON COLUMN "s_website"."tb_web_user"."idcard_code" IS '证件号码';
COMMENT ON COLUMN "s_website"."tb_web_user"."avatar" IS '头像路径';
COMMENT ON COLUMN "s_website"."tb_web_user"."password" IS '密码';
COMMENT ON COLUMN "s_website"."tb_web_user"."salt" IS '盐加密';
COMMENT ON COLUMN "s_website"."tb_web_user"."state" IS '帐号状态:0正常,1停用,2待激活';
COMMENT ON COLUMN "s_website"."tb_web_user"."del_flag" IS '删除标志:0存在,2删除';
COMMENT ON COLUMN "s_website"."tb_web_user"."login_count" IS '登录计数';
COMMENT ON COLUMN "s_website"."tb_web_user"."login_ip" IS '最后登录IP';
COMMENT ON COLUMN "s_website"."tb_web_user"."login_at" IS '最后登录时间';
COMMENT ON COLUMN "s_website"."tb_web_user"."pwd_updated_at" IS '密码最后更新时间';
COMMENT ON COLUMN "s_website"."tb_web_user"."created_by" IS '创建者';
COMMENT ON COLUMN "s_website"."tb_web_user"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_website"."tb_web_user"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_website"."tb_web_user"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_website"."tb_web_user"."remark" IS '备注';
COMMENT ON TABLE "s_website"."tb_web_user" IS '网站用户信息表';

-- ----------------------------
-- 2、网站文档信息表
-- ----------------------------
-- DROP TABLE IF EXISTS "s_website"."tb_web_doc";
CREATE TABLE IF NOT EXISTS "s_website"."tb_web_doc" (
  "doc_id" varchar(21) NOT NULL PRIMARY KEY,
  "doc_title" varchar(50) NOT NULL,
  "doc_type" char(1) NOT NULL,
  "state" char(1) DEFAULT '0',
  "path" text DEFAULT '',
  "is_top" char(1) DEFAULT '0',
  "browse" int DEFAULT 0,
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_website"."tb_web_doc"."doc_id" IS '文档主键';
COMMENT ON COLUMN "s_website"."tb_web_doc"."doc_title" IS '文档标题';
COMMENT ON COLUMN "s_website"."tb_web_doc"."doc_type" IS '文档类型:1客户端,2服务端';
COMMENT ON COLUMN "s_website"."tb_web_doc"."state" IS '文档状态:0上线,1下线';
COMMENT ON COLUMN "s_website"."tb_web_doc"."path" IS '文档存放地址';
COMMENT ON COLUMN "s_website"."tb_web_doc"."is_top" IS '是否置顶:0否,1是';
COMMENT ON COLUMN "s_website"."tb_web_doc"."browse" IS '阅读量';
COMMENT ON COLUMN "s_website"."tb_web_doc"."created_by" IS '创建者';
COMMENT ON COLUMN "s_website"."tb_web_doc"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_website"."tb_web_doc"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_website"."tb_web_doc"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_website"."tb_web_doc"."remark" IS '备注';
COMMENT ON TABLE "s_website"."tb_web_doc" IS '网站文档信息表';

-- ----------------------------
-- 3、网站应用信息表
-- ----------------------------
-- DROP TABLE IF EXISTS "s_website"."tb_web_app";
CREATE TABLE IF NOT EXISTS "s_website"."tb_web_app" (
  "app_id" serial PRIMARY KEY,
  "app_title" varchar(50) NOT NULL,
  "app_type" char(1) NOT NULL,
  "state" char(1) DEFAULT '0',
  "content" text,
  "path" varchar(100),
  "is_top" char(1) DEFAULT '0',
  "published_at" timestamptz NOT NULL,
  "created_by" varchar(30) DEFAULT '',
  "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(30) DEFAULT '',
  "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "remark" text DEFAULT ''
);
COMMENT ON COLUMN "s_website"."tb_web_app"."app_id" IS '应用主键';
COMMENT ON COLUMN "s_website"."tb_web_app"."app_title" IS '应用标题';
COMMENT ON COLUMN "s_website"."tb_web_app"."app_type" IS '应用类型:1生产版本,2历史版本';
COMMENT ON COLUMN "s_website"."tb_web_app"."state" IS '应用状态:0发布,1下线';
COMMENT ON COLUMN "s_website"."tb_web_app"."content" IS '应用简介';
COMMENT ON COLUMN "s_website"."tb_web_app"."path" IS '应用存放地址';
COMMENT ON COLUMN "s_website"."tb_web_app"."is_top" IS '是否置顶:0否,1是';
COMMENT ON COLUMN "s_website"."tb_web_app"."published_at" IS '发布时间';
COMMENT ON COLUMN "s_website"."tb_web_app"."created_by" IS '创建者';
COMMENT ON COLUMN "s_website"."tb_web_app"."created_at" IS '创建时间';
COMMENT ON COLUMN "s_website"."tb_web_app"."updated_by" IS '更新者';
COMMENT ON COLUMN "s_website"."tb_web_app"."updated_at" IS '更新时间';
COMMENT ON COLUMN "s_website"."tb_web_app"."remark" IS '备注';
COMMENT ON TABLE "s_website"."tb_web_app" IS '网站应用信息表';

-- ----------------------------
-- 4、网站在线用户记录
-- ----------------------------
-- DROP TABLE IF EXISTS "s_website"."tb_web_online";
CREATE TABLE IF NOT EXISTS "s_website"."tb_web_online" (
  "session_id" varchar(21) NOT NULL PRIMARY KEY,
  "user_name" varchar(30) DEFAULT '',
  "state" char(1) DEFAULT '',
  "ip_addr" inet DEFAULT NULL,
  "location" varchar(100) DEFAULT '',
  "browser" varchar(50) DEFAULT '',
  "browser_version" varchar(15) DEFAULT '',
  "os" varchar(50) DEFAULT '',
  "os_version" varchar(15) DEFAULT '',
  "device" varchar(15) DEFAULT '',
  "start_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "last_access_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "expire_at" int DEFAULT 30
);
COMMENT ON COLUMN "s_website"."tb_web_online"."session_id" IS '用户会话ID';
COMMENT ON COLUMN "s_website"."tb_web_online"."user_name" IS '登录账号';
COMMENT ON COLUMN "s_website"."tb_web_online"."state" IS '在线状态:0(on_line)在线,1(off_line)离线';
COMMENT ON COLUMN "s_website"."tb_web_online"."ip_addr" IS '登录IP地址';
COMMENT ON COLUMN "s_website"."tb_web_online"."location" IS '登录地点';
COMMENT ON COLUMN "s_website"."tb_web_online"."browser" IS '浏览器类型';
COMMENT ON COLUMN "s_website"."tb_web_online"."browser_version" IS '浏览器版本';
COMMENT ON COLUMN "s_website"."tb_web_online"."os" IS '操作系统';
COMMENT ON COLUMN "s_website"."tb_web_online"."os_version" IS '操作系统版本';
COMMENT ON COLUMN "s_website"."tb_web_online"."device" IS '设备类型';
COMMENT ON COLUMN "s_website"."tb_web_online"."start_at" IS 'session创建时间';
COMMENT ON COLUMN "s_website"."tb_web_online"."last_access_at" IS 'session最后访问时间';
COMMENT ON COLUMN "s_website"."tb_web_online"."expire_at" IS '超时时间,单位为分钟';
COMMENT ON TABLE "s_website"."tb_web_online" IS '网站在线用户记录';

-- ----------------------------
-- 5、短信验证码表
-- ----------------------------
-- DROP TABLE IF EXISTS "s_website"."tb_web_sms";
CREATE TABLE IF NOT EXISTS "s_website"."tb_web_sms" (
  "sms_id" serial PRIMARY KEY,
  "user_id" int NOT NULL,
  "code" char(6),
  "state" char(1) DEFAULT '0',
  "start_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
  "expire_at" int DEFAULT 5
);
COMMENT ON COLUMN "s_website"."tb_web_sms"."sms_id" IS '短信编码';
COMMENT ON COLUMN "s_website"."tb_web_sms"."user_id" IS '用户编码';
COMMENT ON COLUMN "s_website"."tb_web_sms"."code" IS '验证码';
COMMENT ON COLUMN "s_website"."tb_web_sms"."state" IS '验证码状态:0未使用,1已使用,2已过期';
COMMENT ON COLUMN "s_website"."tb_web_sms"."start_at" IS '短信发送时间';
COMMENT ON COLUMN "s_website"."tb_web_sms"."expire_at" IS '超时时间,单位为分钟';
COMMENT ON TABLE "s_website"."tb_web_sms" IS '短信验证码表';
